using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using PC_Control2.Demo.Models;
using PC_Control2.Demo.Configuration;

namespace PC_Control2.Demo.Services
{
    /// <summary>
    /// 分支内容信息类
    /// 包含分支内部元素的详细分析结果，用于动态布局计算
    /// </summary>
    public class BranchContentInfo
    {
        #region 基础信息

        /// <summary>
        /// 分支ID
        /// </summary>
        public string BranchId { get; set; } = string.Empty;

        /// <summary>
        /// 分支类型
        /// </summary>
        public SFCBranchType BranchType { get; set; }

        /// <summary>
        /// 是否为汇聚分支
        /// </summary>
        public bool IsConvergence { get; set; }

        #endregion

        #region 嵌套元素分析

        /// <summary>
        /// 嵌套元素数量
        /// 分支内部包含的SFC元素总数
        /// </summary>
        public int NestedElementCount { get; set; }

        /// <summary>
        /// 最大嵌套宽度（像素）
        /// 分支内部元素占用的最大宽度
        /// </summary>
        public double MaxNestedWidth { get; set; }

        /// <summary>
        /// 最大嵌套高度（像素）
        /// 分支内部元素占用的最大高度
        /// </summary>
        public double MaxNestedHeight { get; set; }

        /// <summary>
        /// 内部元素列表
        /// 分支内部包含的所有SFC元素
        /// </summary>
        public List<SFCElementModelBase> InternalElements { get; set; } = new();

        /// <summary>
        /// 嵌套层级
        /// 当前分支的嵌套深度（0表示顶层分支）
        /// </summary>
        public int NestedLevel { get; set; }

        #endregion

        #region 嵌套分支检测

        /// <summary>
        /// 嵌套子分支列表
        /// 分支内部包含的子分支ID列表
        /// </summary>
        public List<string> NestedBranchIds { get; set; } = new();

        /// <summary>
        /// 是否包含嵌套分支
        /// </summary>
        public bool HasNestedBranches => NestedBranchIds.Count > 0;

        /// <summary>
        /// 嵌套分支数量
        /// </summary>
        public int NestedBranchCount => NestedBranchIds.Count;

        #endregion

        #region 布局需求

        /// <summary>
        /// 建议最小宽度（像素）
        /// 基于内容分析建议的最小分支宽度
        /// </summary>
        public double SuggestedMinWidth { get; set; }

        /// <summary>
        /// 建议最小高度（像素）
        /// 基于内容分析建议的最小分支高度
        /// </summary>
        public double SuggestedMinHeight { get; set; }

        /// <summary>
        /// 需要宽度调整
        /// 指示是否需要进行分支宽度调整（仅当包含嵌套分支时为true）
        /// </summary>
        public bool RequiresWidthAdjustment { get; set; }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 检查是否为空分支
        /// </summary>
        public bool IsEmpty => NestedElementCount == 0 && InternalElements.Count == 0;

        /// <summary>
        /// 检查是否为简单分支（无嵌套分支）
        /// </summary>
        public bool IsSimple => !HasNestedBranches;

        /// <summary>
        /// 检查是否为复杂分支（包含嵌套分支）
        /// </summary>
        public bool IsComplex => HasNestedBranches;

        #endregion
    }

    /// <summary>
    /// 分支内容分析器
    /// 负责分析SFC分支的内容信息，为动态布局提供数据支持
    /// </summary>
    public class BranchContentAnalyzer
    {
        #region 私有字段

        private readonly Dictionary<string, BranchContentInfo> _analysisCache = new();
        private readonly object _cacheLock = new();

        #endregion

        #region 公共方法

        /// <summary>
        /// 分析分支内容
        /// </summary>
        /// <param name="branch">要分析的分支模型</param>
        /// <param name="allElements">所有SFC元素的集合</param>
        /// <returns>分支内容分析结果</returns>
        public BranchContentInfo AnalyzeBranchContent(SFCBranchModel branch, IEnumerable<SFCElementModelBase> allElements)
        {
            if (branch == null)
                throw new ArgumentNullException(nameof(branch));

            // 检查缓存
            lock (_cacheLock)
            {
                if (_analysisCache.TryGetValue(branch.Id, out var cachedInfo))
                {
                    return cachedInfo;
                }
            }

            // 执行分析
            var contentInfo = PerformAnalysis(branch, allElements);

            // 缓存结果
            lock (_cacheLock)
            {
                _analysisCache[branch.Id] = contentInfo;
            }

            return contentInfo;
        }

        /// <summary>
        /// 批量分析分支内容
        /// </summary>
        /// <param name="branches">要分析的分支列表</param>
        /// <param name="allElements">所有SFC元素的集合</param>
        /// <returns>分支内容分析结果字典</returns>
        public Dictionary<string, BranchContentInfo> AnalyzeBranchesContent(
            IEnumerable<SFCBranchModel> branches, 
            IEnumerable<SFCElementModelBase> allElements)
        {
            var results = new Dictionary<string, BranchContentInfo>();
            var elementsList = allElements?.ToList() ?? new List<SFCElementModelBase>();

            foreach (var branch in branches ?? Enumerable.Empty<SFCBranchModel>())
            {
                results[branch.Id] = AnalyzeBranchContent(branch, elementsList);
            }

            return results;
        }

        /// <summary>
        /// 清除分析缓存
        /// </summary>
        /// <param name="branchId">要清除的分支ID，如果为null则清除所有缓存</param>
        public void ClearCache(string? branchId = null)
        {
            lock (_cacheLock)
            {
                if (branchId == null)
                {
                    _analysisCache.Clear();
                }
                else
                {
                    _analysisCache.Remove(branchId);
                }
            }
        }

        /// <summary>
        /// 检测嵌套分支插入操作
        /// 专门用于检测在指定分支内插入嵌套子分支的操作
        /// </summary>
        public bool DetectNestedBranchInsertion(string parentBranchId, string nestedBranchId, IEnumerable<SFCElementModelBase> allElements)
        {
            if (string.IsNullOrEmpty(parentBranchId) || string.IsNullOrEmpty(nestedBranchId))
                return false;

            var allElementsList = allElements.ToList();

            // 查找父分支
            var parentBranch = allElementsList.OfType<SFCBranchModel>()
                .FirstOrDefault(b => b.Id == parentBranchId);

            if (parentBranch == null)
                return false;

            // 查找嵌套分支
            var nestedBranch = allElementsList.OfType<SFCBranchModel>()
                .FirstOrDefault(b => b.Id == nestedBranchId);

            if (nestedBranch == null)
                return false;

            // 检查嵌套分支是否在父分支的内部
            bool isNested = IsElementInsideParentBranch(nestedBranch, parentBranch, allElementsList);

            if (isNested)
            {
                System.Diagnostics.Debug.WriteLine($"[分支内容分析器] 检测到嵌套分支插入: {parentBranchId} -> {nestedBranchId}");
            }

            return isNested;
        }

        /// <summary>
        /// 获取分支的路数
        /// </summary>
        public int GetBranchPathCount(string branchId, IEnumerable<SFCElementModelBase> allElements)
        {
            var allElementsList = allElements.ToList();
            var branch = allElementsList.OfType<SFCBranchModel>()
                .FirstOrDefault(b => b.Id == branchId);

            if (branch == null)
                return 1; // 默认1路

            // 🔧 修复：根据分支类型正确计算路数
            if (branch.BranchType == SFCBranchType.Selection)
            {
                // 选择分支：路数 = 出口连接数
                var outgoingConnections = allElementsList.OfType<SFCConnectionModel>()
                    .Where(conn => conn.SourceId == branchId)
                    .Count();

                var pathCount = Math.Max(1, outgoingConnections);
                System.Diagnostics.Debug.WriteLine($"[路数计算] 选择分支 {branchId}: 出口连接数={outgoingConnections}, 路数={pathCount}");
                return pathCount;
            }
            else if (branch.BranchType == SFCBranchType.Parallel)
            {
                // 并行分支：路数 = 并行路径数
                var outgoingConnections = allElementsList.OfType<SFCConnectionModel>()
                    .Where(conn => conn.SourceId == branchId)
                    .Count();

                var pathCount = Math.Max(2, outgoingConnections); // 并行分支至少2路
                System.Diagnostics.Debug.WriteLine($"[路数计算] 并行分支 {branchId}: 出口连接数={outgoingConnections}, 路数={pathCount}");
                return pathCount;
            }

            System.Diagnostics.Debug.WriteLine($"[路数计算] 未知分支类型 {branchId}: 默认路数=1");
            return 1;
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 执行具体的分支内容分析
        /// </summary>
        private BranchContentInfo PerformAnalysis(SFCBranchModel branch, IEnumerable<SFCElementModelBase> allElements)
        {
            var contentInfo = new BranchContentInfo
            {
                BranchId = branch.Id,
                BranchType = branch.BranchType,
                IsConvergence = branch.IsConvergence
            };

            // 查找分支内部元素
            var internalElements = FindInternalElements(branch, allElements);
            contentInfo.InternalElements = internalElements.ToList();
            contentInfo.NestedElementCount = internalElements.Count();

            // 计算尺寸信息
            CalculateSizeInfo(contentInfo);

            // 计算嵌套层级
            contentInfo.NestedLevel = CalculateNestedLevel(branch, allElements);

            // 检测嵌套分支
            DetectNestedBranches(contentInfo, allElements);

            // 计算布局建议
            CalculateLayoutSuggestions(contentInfo);

            return contentInfo;
        }

        /// <summary>
        /// 查找分支内部元素
        /// </summary>
        private IEnumerable<SFCElementModelBase> FindInternalElements(SFCBranchModel branch, IEnumerable<SFCElementModelBase> allElements)
        {
            var internalElements = new List<SFCElementModelBase>();

            // 添加内部转换条件（仅用于选择分支）
            if (branch.BranchType == SFCBranchType.Selection && branch.InternalTransition != null)
            {
                internalElements.Add(branch.InternalTransition);
            }

            // 查找与分支相关的步骤元素
            var relatedSteps = allElements.OfType<SFCStepModel>()
                .Where(step => branch.InputStepIds.Contains(step.Id) || branch.OutputStepIds.Contains(step.Id));
            
            internalElements.AddRange(relatedSteps);

            return internalElements;
        }

        /// <summary>
        /// 计算尺寸信息
        /// </summary>
        private void CalculateSizeInfo(BranchContentInfo contentInfo)
        {
            if (contentInfo.InternalElements.Count == 0)
            {
                contentInfo.MaxNestedWidth = 0;
                contentInfo.MaxNestedHeight = 0;
                return;
            }

            // 计算所有内部元素的边界框
            double minX = double.MaxValue, minY = double.MaxValue;
            double maxX = double.MinValue, maxY = double.MinValue;

            foreach (var element in contentInfo.InternalElements)
            {
                var bounds = GetElementBounds(element);
                minX = Math.Min(minX, bounds.X);
                minY = Math.Min(minY, bounds.Y);
                maxX = Math.Max(maxX, bounds.X + bounds.Width);
                maxY = Math.Max(maxY, bounds.Y + bounds.Height);
            }

            contentInfo.MaxNestedWidth = maxX - minX;
            contentInfo.MaxNestedHeight = maxY - minY;
        }

        /// <summary>
        /// 获取元素边界框
        /// </summary>
        private Rect GetElementBounds(SFCElementModelBase element)
        {
            return new Rect(element.Position.X, element.Position.Y, element.Size.Width, element.Size.Height);
        }

        /// <summary>
        /// 计算嵌套层级
        /// </summary>
        private int CalculateNestedLevel(SFCBranchModel branch, IEnumerable<SFCElementModelBase> allElements)
        {
            try
            {
                // 获取所有分支元素
                var allBranches = allElements.OfType<SFCBranchModel>().ToList();
                var allConnections = allElements.OfType<SFCConnectionModel>().ToList();
                
                if (allBranches.Count <= 1)
                {
                    return 0; // 只有一个分支或没有分支，嵌套层级为0
                }

                // 计算当前分支的嵌套层级
                int nestedLevel = 0;
                
                // 方法1：基于空间包含关系检测嵌套
                nestedLevel = Math.Max(nestedLevel, CalculateNestedLevelBySpacialContainment(branch, allBranches));
                
                // 方法2：基于连接关系检测嵌套
                nestedLevel = Math.Max(nestedLevel, CalculateNestedLevelByConnectionAnalysis(branch, allBranches, allConnections));
                
                // 方法3：基于分支链关系检测嵌套
                nestedLevel = Math.Max(nestedLevel, CalculateNestedLevelByBranchChain(branch, allBranches));
                
                return nestedLevel;
            }
            catch (Exception ex)
            {
                // 记录错误但不抛出异常，返回默认值
                System.Diagnostics.Debug.WriteLine($"计算嵌套层级时发生错误: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// 基于空间包含关系计算嵌套层级
        /// </summary>
        private int CalculateNestedLevelBySpacialContainment(SFCBranchModel targetBranch, List<SFCBranchModel> allBranches)
        {
            int level = 0;
            var targetRect = new Rect(targetBranch.Position, targetBranch.Size);
            
            foreach (var otherBranch in allBranches)
            {
                if (otherBranch.Id == targetBranch.Id) continue;
                
                var otherRect = new Rect(otherBranch.Position, otherBranch.Size);
                
                // 检查目标分支是否被其他分支包含
                if (IsRectangleContained(targetRect, otherRect))
                {
                    level++;
                }
            }
            
            return level;
        }
        
        /// <summary>
        /// 基于连接关系分析计算嵌套层级
        /// </summary>
        private int CalculateNestedLevelByConnectionAnalysis(SFCBranchModel targetBranch, List<SFCBranchModel> allBranches, List<SFCConnectionModel> allConnections)
        {
            // 构建分支的连接图
            var branchConnections = BuildBranchConnectionGraph(allBranches, allConnections);
            
            // 从目标分支开始，向上追溯连接路径
            return CalculateConnectionDepth(targetBranch.Id, branchConnections, new HashSet<string>());
        }
        
        /// <summary>
        /// 基于分支链关系计算嵌套层级
        /// </summary>
        private int CalculateNestedLevelByBranchChain(SFCBranchModel targetBranch, List<SFCBranchModel> allBranches)
        {
            // 如果分支有NextBranchId，说明它是分支链的一部分
            if (string.IsNullOrEmpty(targetBranch.NextBranchId))
            {
                return 0;
            }
            
            // 计算分支链的深度
            int chainDepth = 0;
            var visited = new HashSet<string>();
            var currentBranchId = targetBranch.Id;
            
            while (!string.IsNullOrEmpty(currentBranchId) && !visited.Contains(currentBranchId))
            {
                visited.Add(currentBranchId);
                var currentBranch = allBranches.FirstOrDefault(b => b.Id == currentBranchId);
                
                if (currentBranch != null && !string.IsNullOrEmpty(currentBranch.NextBranchId))
                {
                    chainDepth++;
                    currentBranchId = currentBranch.NextBranchId;
                }
                else
                {
                    break;
                }
            }
            
            return chainDepth;
        }
        
        /// <summary>
        /// 检查一个矩形是否被另一个矩形包含
        /// </summary>
        private bool IsRectangleContained(Rect inner, Rect outer)
        {
            return outer.Contains(inner.TopLeft) && outer.Contains(inner.BottomRight);
        }
        
        /// <summary>
        /// 构建分支连接图
        /// </summary>
        private Dictionary<string, List<string>> BuildBranchConnectionGraph(List<SFCBranchModel> branches, List<SFCConnectionModel> connections)
        {
            var graph = new Dictionary<string, List<string>>();
            
            // 初始化图
            foreach (var branch in branches)
            {
                graph[branch.Id] = new List<string>();
            }
            
            // 添加连接关系
            foreach (var connection in connections)
            {
                var sourceBranch = branches.FirstOrDefault(b => b.Id == connection.SourceId);
                var targetBranch = branches.FirstOrDefault(b => b.Id == connection.TargetId);
                
                if (sourceBranch != null && targetBranch != null)
                {
                    if (!graph[connection.TargetId].Contains(connection.SourceId))
                    {
                        graph[connection.TargetId].Add(connection.SourceId);
                    }
                }
            }
            
            return graph;
        }
        
        /// <summary>
        /// 计算连接深度（递归）
        /// </summary>
        private int CalculateConnectionDepth(string branchId, Dictionary<string, List<string>> graph, HashSet<string> visited)
        {
            if (visited.Contains(branchId) || !graph.ContainsKey(branchId))
            {
                return 0;
            }
            
            visited.Add(branchId);
            int maxDepth = 0;
            
            foreach (var parentBranchId in graph[branchId])
            {
                int depth = 1 + CalculateConnectionDepth(parentBranchId, graph, new HashSet<string>(visited));
                maxDepth = Math.Max(maxDepth, depth);
            }
            
            return maxDepth;
        }

        /// <summary>
        /// 检测嵌套分支
        /// </summary>
        private void DetectNestedBranches(BranchContentInfo contentInfo, IEnumerable<SFCElementModelBase> allElements)
        {
            var allElementsList = allElements.ToList();
            var nestedBranchIds = new List<string>();

            // 查找分支内部的所有嵌套分支
            foreach (var element in contentInfo.InternalElements)
            {
                // 检查元素是否为分支类型
                if (element is SFCBranchModel nestedBranch)
                {
                    nestedBranchIds.Add(nestedBranch.Id);
                }
                // 检查元素下方是否连接了分支
                else
                {
                    var connectedBranches = FindConnectedBranches(element, allElementsList);
                    nestedBranchIds.AddRange(connectedBranches);
                }
            }

            // 更新嵌套分支信息
            contentInfo.NestedBranchIds = nestedBranchIds.Distinct().ToList();

            System.Diagnostics.Debug.WriteLine($"[分支内容分析器] 检测到嵌套分支: {contentInfo.BranchId} -> {string.Join(", ", contentInfo.NestedBranchIds)}");
        }

        /// <summary>
        /// 查找与元素连接的分支
        /// </summary>
        private List<string> FindConnectedBranches(SFCElementModelBase element, List<SFCElementModelBase> allElements)
        {
            var connectedBranches = new List<string>();

            // 查找从当前元素出发的连接线
            var connections = allElements.OfType<SFCConnectionModel>()
                .Where(conn => conn.SourceId == element.Id)
                .ToList();

            foreach (var connection in connections)
            {
                // 查找连接的目标元素
                var targetElement = allElements.FirstOrDefault(e => e.Id == connection.TargetId);
                if (targetElement is SFCBranchModel targetBranch)
                {
                    connectedBranches.Add(targetBranch.Id);
                }
            }

            return connectedBranches;
        }

        /// <summary>
        /// 检查元素是否在父分支内部
        /// </summary>
        private bool IsElementInsideParentBranch(SFCElementModelBase element, SFCBranchModel parentBranch, List<SFCElementModelBase> allElements)
        {
            // 简化实现：检查元素是否通过连接关系与父分支相关
            // 实际实现中可能需要更复杂的空间位置判断

            // 查找从父分支出发的连接路径
            var visited = new HashSet<string>();
            return IsElementReachableFromBranch(element.Id, parentBranch.Id, allElements, visited);
        }

        /// <summary>
        /// 检查元素是否可以从分支到达
        /// </summary>
        private bool IsElementReachableFromBranch(string elementId, string branchId, List<SFCElementModelBase> allElements, HashSet<string> visited)
        {
            if (visited.Contains(branchId))
                return false;

            visited.Add(branchId);

            // 查找从分支出发的所有连接
            var connections = allElements.OfType<SFCConnectionModel>()
                .Where(conn => conn.SourceId == branchId)
                .ToList();

            foreach (var connection in connections)
            {
                if (connection.TargetId == elementId)
                    return true;

                // 递归检查连接的目标元素
                if (IsElementReachableFromBranch(elementId, connection.TargetId, allElements, visited))
                    return true;
            }

            return false;
        }

        /// <summary>
        /// 计算布局建议
        /// </summary>
        private void CalculateLayoutSuggestions(BranchContentInfo contentInfo)
        {
            // 基础尺寸
            double baseWidth = SiemensGraphLayoutRules.GetBaseBranchHeight(contentInfo.BranchType);
            double baseHeight = SiemensGraphLayoutRules.MIN_BRANCH_HEIGHT;

            // 内容适应
            double contentWidth = contentInfo.MaxNestedWidth + 2 * SiemensGraphLayoutRules.BRANCH_CONTENT_MARGIN;
            double contentHeight = contentInfo.MaxNestedHeight + 2 * SiemensGraphLayoutRules.BRANCH_CONTENT_MARGIN;

            // 嵌套缩进
            double nestedIndent = SiemensGraphLayoutRules.CalculateNestedIndent(contentInfo.NestedLevel);

            // 建议尺寸
            contentInfo.SuggestedMinWidth = Math.Max(baseWidth, contentWidth + nestedIndent);
            contentInfo.SuggestedMinHeight = Math.Max(baseHeight, contentHeight);

            // 应用扩展因子
            if (contentInfo.NestedElementCount > 0)
            {
                contentInfo.SuggestedMinWidth *= SiemensGraphLayoutRules.CONTENT_WIDTH_EXPANSION_FACTOR;
                contentInfo.SuggestedMinHeight *= SiemensGraphLayoutRules.CONTENT_HEIGHT_EXPANSION_FACTOR;
            }

            // 限制最大尺寸
            contentInfo.SuggestedMinWidth = Math.Min(contentInfo.SuggestedMinWidth, SiemensGraphLayoutRules.MAX_CONTENT_WIDTH);
            contentInfo.SuggestedMinHeight = Math.Min(contentInfo.SuggestedMinHeight, SiemensGraphLayoutRules.MAX_CONTENT_HEIGHT);

            // 判断是否需要宽度调整（只有包含嵌套分支时才需要）
            contentInfo.RequiresWidthAdjustment = contentInfo.HasNestedBranches;
        }

        #endregion
    }
}
