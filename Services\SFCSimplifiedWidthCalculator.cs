using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using PC_Control2.Demo.Models;
using PC_Control2.Demo.Configuration;

namespace PC_Control2.Demo.Services
{
    /// <summary>
    /// SFC简化宽度计算器
    /// 基于正确的分支闭合状态理解，实现简单的路数×147.0px宽度计算
    /// </summary>
    public class SFCSimplifiedWidthCalculator
    {
        #region 私有字段

        /// <summary>
        /// 单位宽度常量
        /// </summary>
        private const double UNIT_WIDTH = 147.0;

        /// <summary>
        /// 分支闭合状态管理器
        /// </summary>
        private readonly SFCBranchClosureManager _closureManager;

        /// <summary>
        /// 分支内容分析器
        /// </summary>
        private readonly BranchContentAnalyzer _contentAnalyzer;

        /// <summary>
        /// 计算缓存
        /// </summary>
        private readonly Dictionary<string, double> _widthCache = new();
        private readonly object _cacheLock = new();

        #endregion

        #region 事件

        /// <summary>
        /// 宽度计算完成事件
        /// </summary>
        public event EventHandler<(string branchId, double width, int pathCount)>? WidthCalculated;

        /// <summary>
        /// 宽度调整事件
        /// </summary>
        public event EventHandler<(string branchId, double oldWidth, double newWidth)>? WidthAdjusted;

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public SFCSimplifiedWidthCalculator(SFCBranchClosureManager closureManager)
        {
            _closureManager = closureManager ?? throw new ArgumentNullException(nameof(closureManager));
            _contentAnalyzer = new BranchContentAnalyzer();

            // 订阅闭合状态管理器的事件
            _closureManager.WidthAdjustmentRequested += OnWidthAdjustmentRequested;
            _closureManager.NestedBranchInserted += OnNestedBranchInserted;

            System.Diagnostics.Debug.WriteLine("[简化宽度计算器] 初始化完成");
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 计算分支宽度
        /// 基于路数的简单计算：宽度 = 路数 × 147.0px
        /// </summary>
        public double CalculateBranchWidth(string branchId, IEnumerable<SFCElementModelBase> allElements)
        {
            if (string.IsNullOrEmpty(branchId))
                return UNIT_WIDTH;

            // 检查缓存
            lock (_cacheLock)
            {
                if (_widthCache.TryGetValue(branchId, out var cachedWidth))
                {
                    return cachedWidth;
                }
            }

            // 获取分支路数
            int pathCount = _contentAnalyzer.GetBranchPathCount(branchId, allElements);
            
            // 简单计算：宽度 = 路数 × 单位宽度
            double width = pathCount * UNIT_WIDTH;

            // 缓存结果
            lock (_cacheLock)
            {
                _widthCache[branchId] = width;
            }

            // 触发事件
            WidthCalculated?.Invoke(this, (branchId, width, pathCount));

            System.Diagnostics.Debug.WriteLine($"[简化宽度计算器] 计算分支宽度: {branchId} = {pathCount}路 × {UNIT_WIDTH}px = {width}px");
            return width;
        }

        /// <summary>
        /// 批量计算分支宽度
        /// </summary>
        public Dictionary<string, double> CalculateBatchWidths(IEnumerable<string> branchIds, IEnumerable<SFCElementModelBase> allElements)
        {
            var results = new Dictionary<string, double>();
            var allElementsList = allElements.ToList();

            foreach (var branchId in branchIds)
            {
                results[branchId] = CalculateBranchWidth(branchId, allElementsList);
            }

            System.Diagnostics.Debug.WriteLine($"[简化宽度计算器] 批量计算完成: {results.Count} 个分支");
            return results;
        }

        /// <summary>
        /// 检查分支是否需要宽度调整
        /// 只有未闭合分支且包含嵌套分支时才需要调整
        /// </summary>
        public bool NeedsWidthAdjustment(string branchId, IEnumerable<SFCElementModelBase> allElements)
        {
            if (string.IsNullOrEmpty(branchId))
                return false;

            // 检查分支是否可以调整宽度（未闭合状态）
            if (!_closureManager.CanAdjustBranchWidth(branchId))
                return false;

            // 检查是否包含嵌套分支
            var allElementsList = allElements.ToList();
            var branch = allElementsList.OfType<SFCBranchModel>().FirstOrDefault(b => b.Id == branchId);
            if (branch == null)
                return false;

            var contentInfo = _contentAnalyzer.AnalyzeBranchContent(branch, allElementsList);
            return contentInfo.RequiresWidthAdjustment;
        }

        /// <summary>
        /// 强制重新计算分支宽度
        /// </summary>
        public double RecalculateBranchWidth(string branchId, IEnumerable<SFCElementModelBase> allElements)
        {
            if (string.IsNullOrEmpty(branchId))
                return UNIT_WIDTH;

            // 清除缓存
            lock (_cacheLock)
            {
                _widthCache.Remove(branchId);
            }

            // 重新计算
            return CalculateBranchWidth(branchId, allElements);
        }

        /// <summary>
        /// 获取分支的路数
        /// </summary>
        public int GetBranchPathCount(string branchId, IEnumerable<SFCElementModelBase> allElements)
        {
            return _contentAnalyzer.GetBranchPathCount(branchId, allElements);
        }

        /// <summary>
        /// 清除缓存
        /// </summary>
        public void ClearCache()
        {
            lock (_cacheLock)
            {
                _widthCache.Clear();
                System.Diagnostics.Debug.WriteLine("[简化宽度计算器] 清除缓存");
            }
        }

        /// <summary>
        /// 获取缓存统计信息
        /// </summary>
        public (int cachedCount, double totalWidth) GetCacheStatistics()
        {
            lock (_cacheLock)
            {
                return (_widthCache.Count, _widthCache.Values.Sum());
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 🔧 修复：处理宽度调整请求，清理相关缓存
        /// </summary>
        private void OnWidthAdjustmentRequested(object? sender, (string branchId, double oldWidth, double newWidth) args)
        {
            var (branchId, oldWidth, newWidth) = args;

            // 🔧 修复：清理相关缓存，强制重新计算
            lock (_cacheLock)
            {
                // 清理当前分支缓存
                _widthCache.Remove(branchId);

                // 清理所有可能受影响的父分支缓存
                var affectedBranches = GetAffectedParentBranches(branchId);
                foreach (var parentId in affectedBranches)
                {
                    _widthCache.Remove(parentId);
                    System.Diagnostics.Debug.WriteLine($"[简化宽度计算器] 清理父分支缓存: {parentId}");
                }

                System.Diagnostics.Debug.WriteLine($"[简化宽度计算器] 缓存清理完成: {branchId}, 受影响父分支数: {affectedBranches.Count}");
            }

            // 触发宽度调整事件
            WidthAdjusted?.Invoke(this, (branchId, oldWidth, newWidth));

            System.Diagnostics.Debug.WriteLine($"[简化宽度计算器] 宽度调整: {branchId} {oldWidth}px -> {newWidth}px");
        }

        /// <summary>
        /// 处理嵌套分支插入
        /// </summary>
        private void OnNestedBranchInserted(object? sender, (string parentBranchId, string nestedBranchId) args)
        {
            var (parentBranchId, nestedBranchId) = args;

            // 清除相关缓存，强制重新计算
            lock (_cacheLock)
            {
                _widthCache.Remove(parentBranchId);
                _widthCache.Remove(nestedBranchId);
            }

            System.Diagnostics.Debug.WriteLine($"[简化宽度计算器] 嵌套分支插入，清除缓存: {parentBranchId}, {nestedBranchId}");
        }

        /// <summary>
        /// 🔧 修复：获取可能受影响的父分支列表
        /// </summary>
        /// <param name="branchId">分支ID</param>
        /// <returns>受影响的父分支ID列表</returns>
        private List<string> GetAffectedParentBranches(string branchId)
        {
            var affectedBranches = new List<string>();

            try
            {
                // 这里可以根据实际的分支层次结构来查找父分支
                // 暂时返回空列表，避免过度清理缓存
                // 在实际应用中，可以通过分支管理器获取父分支信息

                System.Diagnostics.Debug.WriteLine($"[简化宽度计算器] 查找受影响的父分支: {branchId}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[简化宽度计算器] 查找父分支失败: {branchId}, 错误: {ex.Message}");
            }

            return affectedBranches;
        }

        #endregion

        #region IDisposable

        public void Dispose()
        {
            if (_closureManager != null)
            {
                _closureManager.WidthAdjustmentRequested -= OnWidthAdjustmentRequested;
                _closureManager.NestedBranchInserted -= OnNestedBranchInserted;
            }
        }

        #endregion
    }

    /// <summary>
    /// 宽度计算结果
    /// </summary>
    public class WidthCalculationResult
    {
        /// <summary>
        /// 分支ID
        /// </summary>
        public string BranchId { get; set; } = string.Empty;

        /// <summary>
        /// 计算得出的宽度
        /// </summary>
        public double Width { get; set; }

        /// <summary>
        /// 路数
        /// </summary>
        public int PathCount { get; set; }

        /// <summary>
        /// 是否为调整后的宽度
        /// </summary>
        public bool IsAdjusted { get; set; }

        /// <summary>
        /// 计算时间
        /// </summary>
        public DateTime CalculatedTime { get; set; } = DateTime.Now;

        public override string ToString()
        {
            return $"{BranchId}: {Width}px ({PathCount}路)";
        }
    }
}
