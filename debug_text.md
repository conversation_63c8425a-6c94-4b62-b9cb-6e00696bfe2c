[InsertSelectionBranchAfterStep] 选中元素: 初始步
[选择分支] 首次插入，现有分支: 0个
[GetActualConnectPointPosition] 步骤连接点: 索引0, 位置250,322
[CalculateConnectPointAlignedPositionForBranch] Selection分支精确对齐:
  源连接点: 250,322
  目标连接点偏移: 21,7
  计算位置: 229.5,319.5
[选择分支] 使用连接点精确对齐计算位置: 229.5,319.5
[选择分支] 新分支位置: 229.5,319.5
[选择分支] 创建: Initial, 位置: 229.5,319.5
[分支集合] 添加分支，位置: 229.5,319.5
[SFCBranchViewModel] 初始化适配器: BranchType=Selection, ElementType=SelectionBranch
[SFCBranchViewModel] ✅ 选择分支适配器配置完成，数量: 4
[SFCBranchViewModel] 🔧 索引1适配器类型: Input (修复为Input)
[SFCBranchViewModel] 🔧 索引2适配器类型: Output (修复为Output)
[SFCBranchViewModel] 🔧 临时修复：为选择分支设置默认转换条件编号 T1
[SFCBranchViewModel] 动态布局属性初始化:  -> 147x80
[SFCBranchViewModel] 🔍 关键属性值调试:
  - LeftLineX1: 20, LeftLineX2: 20
  - LeftLineLeft: 1
[MainHorizontalLineX2] 超级简化: 宽度147 → 线条长度147 → X2=44.0
  - MainHorizontalLineX1: -103, MainHorizontalLineX2: 44
  - MainHorizontalLineLeft: 124
  - ViewType: Initial, IsSelected: False
  - InternalTransitionNumber: 1, InternalTransitionHasLabel: True
[SFCBranchViewModel] ⚠️ 无法获取EnhancedSFCViewModel实例
[分支集合] ViewModel位置: 229.5,319.5
[SFCCanvas] 为新创建的分支添加位置变化监听: c4b8d342-386c-46dd-aa77-0248eff64fcd
[分支闭合管理器] 注册分支: c4b8d342-386c-46dd-aa77-0248eff64fcd, 初始路数: 1
[嵌套检测] 开始查找分支 c4b8d342-386c-46dd-aa77-0248eff64fcd 的父分支
[嵌套检测] 找到 0 个输入连接
[空间检测] 开始基于空间关系查找父分支（就近原则）
[空间检测] 找到 0 个现有分支
[空间检测] 未找到空间关系的父分支
[嵌套检测] 未找到父分支
[EnhancedSFCViewModel] 未找到父分支，c4b8d342-386c-46dd-aa77-0248eff64fcd 为顶层分支
[AddConnection] 开始执行: 942f873e-cdea-4f8c-9a08-ae048fe4bc88 -> c4b8d342-386c-46dd-aa77-0248eff64fcd
[AddConnection] 对象查找结果: sourceObject=SFCStepModel, targetObject=SFCBranchModel
[AddConnection] 位置获取: 源ViewModel位置=200,200, 源Model位置=200,200
[AddConnection] 位置获取: 目标ViewModel位置=229.5,319.5, 目标Model位置=229.5,319.5
[GetPreciseConnectPointPosition] ⚠️ 回退到计算方法
[GetPreciseConnectPointPosition] ⚠️ 回退到计算方法
[CalculateElementConnectPoint] SFCBranchModel: 类型=Selection, 位置=229.5,319.5, 输出点=False
[对比调试] 如果是步骤输入点，位置应该是: 279.5,325.5
[CalculateElementConnectPoint] ✅ 选择分支左上连接点(索引0): 251,331
[AddConnection] 创建连接: 942f873e-cdea-4f8c-9a08-ae048fe4bc88 -> c4b8d342-386c-46dd-aa77-0248eff64fcd
[AddConnection] 源位置: 200,200, 目标位置: 229.5,319.5
[AddConnection] 源对象类型: SFCStepModel, 目标对象类型: SFCBranchModel
[AddConnection] 源连接点: 250,322, 目标连接点: 251,331
[AddConnection] 连接创建完成，路径点数量: 2
[AddConnectionViewModel] 连接 e46fde75-6434-4c77-8ba9-825cd9f4fb1d 路径点数量: 2
[AddConnectionViewModel] ViewModel路径点数量: 2
[AddConnectionViewModel] 起点: 250,322, 终点: 251,331
[AddConnection] 🔄 开始更新连接点状态: 942f873e-cdea-4f8c-9a08-ae048fe4bc88 -> c4b8d342-386c-46dd-aa77-0248eff64fcd
[UpdateElementConnectPointState] 🔍 开始更新连接点状态:
  元素类型: SFCStepViewModel
  连接点索引: 0
  是否输入: False
  连接ID: e46fde75-6434-4c77-8ba9-825cd9f4fb1d
[UpdateElementConnectPointState] ✅ 连接点状态更新成功: SFCStepViewModel, Direction=Output, Index=0, AdapterIndex=0, ConnectionId: e46fde75-6434-4c77-8ba9-825cd9f4fb1d
[UpdateElementConnectPointState] 🔍 开始更新连接点状态:
  元素类型: SFCBranchViewModel
  连接点索引: 0
  是否输入: True
  连接ID: e46fde75-6434-4c77-8ba9-825cd9f4fb1d
[UpdateElementConnectPointState] 🎯 找到分支元素适配器: c4b8d342-386c-46dd-aa77-0248eff64fcd, 分支类型: Selection, 适配器数量: 4
[UpdateElementConnectPointState] ✅ 连接点状态更新成功: SFCBranchViewModel, Direction=Input, Index=0, AdapterIndex=0, ConnectionId: e46fde75-6434-4c77-8ba9-825cd9f4fb1d
[UpdateConnectPointStates] 连接点状态更新完成: 942f873e-cdea-4f8c-9a08-ae048fe4bc88[0] -> c4b8d342-386c-46dd-aa77-0248eff64fcd[0]
[选择分支] 跳过垂直对齐，保持计算位置: 229.5,319.5
[选择分支] 左分支使用竖直连接线，无需创建转换条件
[选择分支] 右分支转换条件已内嵌在选择分支视图中，无需单独创建
System.Windows.Data Error: 40 : BindingExpression path error: 'Description' property not found on 'object' ''SFCBranchViewModel' (HashCode=46708959)'. BindingExpression:Path=SelectedElement.Description; DataItem='EnhancedSFCViewModel' (HashCode=34232944); target element is 'TextBlock' (Name=''); target property is 'Text' (type 'String')
[InitializeConnectPoints] 开始初始化连接点，ViewModel.Id: c4b8d342-386c-46dd-aa77-0248eff64fcd
[InitializeConnectPoints] XAML连接点查找结果: LeftTop=True, LeftBottom=True, RightTop=True, RightBottom=True
[SFCSelectionBranchView] 开始设置连接点ElementId: c4b8d342-386c-46dd-aa77-0248eff64fcd
[SFCSelectionBranchView] ✅ LeftTop连接点ElementId设置: c4b8d342-386c-46dd-aa77-0248eff64fcd
[SFCSelectionBranchView] ✅ LeftBottom连接点ElementId设置: c4b8d342-386c-46dd-aa77-0248eff64fcd
[SFCSelectionBranchView] ✅ RightTop连接点ElementId设置: c4b8d342-386c-46dd-aa77-0248eff64fcd
[SFCSelectionBranchView] ✅ RightBottom连接点ElementId设置: c4b8d342-386c-46dd-aa77-0248eff64fcd
[InitializeConnectPoints] LeftTop连接点ElementId: 'c4b8d342-386c-46dd-aa77-0248eff64fcd', 期望: 'c4b8d342-386c-46dd-aa77-0248eff64fcd'
[InitializeConnectPoints] ✅ 已修复LeftTop连接点ElementId: 'c4b8d342-386c-46dd-aa77-0248eff64fcd'
[SFCSelectionBranchView] 适配器检查: Count=4, 期望>=4
[SFCSelectionBranchView] ✅ LeftTop适配器绑定: Direction=Input, Index=0
[SFCSelectionBranchView] ✅ LeftBottom适配器绑定: Direction=Input, Index=1
[SFCSelectionBranchView] ✅ RightTop适配器绑定: Direction=Output, Index=2
[SFCSelectionBranchView] ✅ RightBottom适配器绑定: Direction=Output, Index=3
[SFCSelectionBranchView] 开始同步现有连接状态: c4b8d342-386c-46dd-aa77-0248eff64fcd
[SFCSelectionBranchView] 找到 1 个相关连接
[SFCSelectionBranchView] ✅ 同步输入连接: e46fde75-6434-4c77-8ba9-825cd9f4fb1d, 索引: 0
[MainHorizontalLineX2] 超级简化: 宽度147 → 线条长度147 → X2=44.0
[MainHorizontalLineX2] 超级简化: 宽度147 → 线条长度147 → X2=44.0
[RightConnectionLineLeft] 主横线终点=168.0, 右侧竖线位置=165.5
[MainHorizontalLineX2] 超级简化: 宽度147 → 线条长度147 → X2=44.0
[TransitionConnectionLineLeft] 主横线终点=168.0, 连接虚线位置=107.0
[MainHorizontalLineX2] 超级简化: 宽度147 → 线条长度147 → X2=44.0
[TransitionCrossLineLeft] 主横线终点=168.0, 十字横线位置=107.0
[MainHorizontalLineX2] 超级简化: 宽度147 → 线条长度147 → X2=44.0
[TransitionContactLeftLineLeft] 主横线终点=168.0, 触点左竖线位置=128.0
[MainHorizontalLineX2] 超级简化: 宽度147 → 线条长度147 → X2=44.0
[TransitionContactRightLineLeft] 主横线终点=168.0, 触点右竖线位置=135.0
[MainHorizontalLineX2] 超级简化: 宽度147 → 线条长度147 → X2=44.0
[RightTopConnectPointLeft] 主横线终点=168.0, 右上连接点位置=162.0
[MainHorizontalLineX2] 超级简化: 宽度147 → 线条长度147 → X2=44.0
[RightBottomConnectPointLeft] 主横线终点=168.0, 右下连接点位置=162.0
[MainHorizontalLineX2] 超级简化: 宽度147 → 线条长度147 → X2=44.0
[TransitionIdentifierLeft] 主横线终点=168.0, T1位置=176.0
[InitializeConnectPoints] 连接点已初始化，跳过重复初始化: c4b8d342-386c-46dd-aa77-0248eff64fcd
System.Windows.Data Error: 4 : Cannot find source for binding with reference 'RelativeSource FindAncestor, AncestorType='System.Windows.Controls.UserControl', AncestorLevel='1''. BindingExpression:Path=DataContext.InsertSelectionBranchFromAnyPartCommand; DataItem=null; target element is 'MenuItem' (Name=''); target property is 'Command' (type 'ICommand')
System.Windows.Data Error: 4 : Cannot find source for binding with reference 'RelativeSource FindAncestor, AncestorType='System.Windows.Controls.UserControl', AncestorLevel='1''. BindingExpression:Path=DataContext.InsertSiblingBranchCommand; DataItem=null; target element is 'MenuItem' (Name=''); target property is 'Command' (type 'ICommand')
System.Windows.Data Error: 4 : Cannot find source for binding with reference 'RelativeSource FindAncestor, AncestorType='System.Windows.Controls.UserControl', AncestorLevel='1''. BindingExpression:Path=DataContext.InsertParallelBranchFromAnyPartCommand; DataItem=null; target element is 'MenuItem' (Name=''); target property is 'Command' (type 'ICommand')
[连接点重叠检测] 点1: (250.0, 322.0), 点2: (251.0, 331.0), 距离: 9.1px, 重叠: True
[CreateConnectionPath] 连接 e46fde75-6434-4c77-8ba9-825cd9f4fb1d 连接点重叠，隐藏连接线
[CreateConnectionPath] 起点: (250.0, 322.0), 终点: (251.0, 331.0)
连接 e46fde75-6434-4c77-8ba9-825cd9f4fb1d 连接点重叠，使用连接点重叠方式
[AddConnection] 延迟更新连接线 e46fde75-6434-4c77-8ba9-825cd9f4fb1d 的路径点
[CalculateElementConnectPoint] ✅ 选择分支ViewModel左上连接点(索引0): 250,326.5
[AddConnection] 延迟更新 - 源对象类型: SFCStepViewModel, 目标对象类型: SFCBranchViewModel
[AddConnection] 延迟更新 - 源位置: 200,200, 目标位置: 229.5,319.5
[AddConnection] 延迟更新后的路径点: 250,322 -> 250,326.5
[AddConnection] PathPoints集合已更新，应该触发重新评估
“PC_Control2.Demo.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.NETCore.App\8.0.17\System.Runtime.Intrinsics.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
[SFCCanvas] 位置变化: SFCBranchViewModel.IsSelected
[SFCCanvas] 位置变化: SFCBranchViewModel.SelectedPart
[SFCSelectionBranchView] 拖动更新位置: c4b8d342-386c-46dd-aa77-0248eff64fcd -> 229.5,319.5
[SFCSelectionBranchView] 左侧区域选中并开始拖拽: c4b8d342-386c-46dd-aa77-0248eff64fcd
[SFCBranchView] 接收到鼠标点击事件: Id=c4b8d342-386c-46dd-aa77-0248eff64fcd, BranchType=Selection
[SFCBranchView] 选择分支，只处理选中逻辑
[SFCCanvas] 位置变化: SFCBranchViewModel.IsSelected
System.Windows.Data Error: 40 : BindingExpression path error: 'Description' property not found on 'object' ''SFCBranchViewModel' (HashCode=46708959)'. BindingExpression:Path=SelectedElement.Description; DataItem='EnhancedSFCViewModel' (HashCode=34232944); target element is 'TextBlock' (Name=''); target property is 'Text' (type 'String')
[CalculateElementConnectPoint] ✅ 选择分支ViewModel左下连接点(索引1): 250,354.5
[GetActualConnectPointPosition] 分支连接点: 类型Selection, 索引1, 位置250,354.5
[InsertTransitionAfterSelected] 动态获取左侧连接点位置: 250,354.5, 转换条件X=190.2
[AddTransitionToCollections] Model位置: (190.2, 354.5)
[AddTransitionToCollections] ViewModel位置: (190.2, 354.5)
[SFCCanvas] 为新创建的转换条件添加位置变化监听: 39f7ddb0-4816-480b-9b93-a9eac4d9c89a
[连接创建] 准备创建连接: c4b8d342-386c-46dd-aa77-0248eff64fcd -> 39f7ddb0-4816-480b-9b93-a9eac4d9c89a
[AddConnection] 开始执行: c4b8d342-386c-46dd-aa77-0248eff64fcd -> 39f7ddb0-4816-480b-9b93-a9eac4d9c89a
[AddConnection] 对象查找结果: sourceObject=SFCBranchModel, targetObject=SFCTransitionModel
[AddConnection] 位置获取: 源ViewModel位置=229.5,319.5, 源Model位置=229.5,319.5
[AddConnection] 位置获取: 目标ViewModel位置=190.2,354.5, 目标Model位置=190.2,354.5
[GetPreciseConnectPointPosition] ⚠️ 回退到计算方法
[CalculateElementConnectPoint] SFCBranchModel: 类型=Selection, 位置=229.5,319.5, 输出点=True
[CalculateElementConnectPoint] ✅ 选择分支左下连接点(索引1): 258.5,354.5
[GetPreciseConnectPointPosition] ⚠️ 回退到计算方法
[AddConnection] 创建连接: c4b8d342-386c-46dd-aa77-0248eff64fcd -> 39f7ddb0-4816-480b-9b93-a9eac4d9c89a
[AddConnection] 源位置: 229.5,319.5, 目标位置: 190.2,354.5
[AddConnection] 源对象类型: SFCBranchModel, 目标对象类型: SFCTransitionModel
[AddConnection] 源连接点: 258.5,354.5, 目标连接点: 250.5,354.5
[AddConnection] 连接创建完成，路径点数量: 2
[AddConnectionViewModel] 连接 011cdf46-8785-493c-9249-5d6869517485 路径点数量: 2
[AddConnectionViewModel] ViewModel路径点数量: 2
[AddConnectionViewModel] 起点: 258.5,354.5, 终点: 250.5,354.5
[AddConnection] 🔄 开始更新连接点状态: c4b8d342-386c-46dd-aa77-0248eff64fcd -> 39f7ddb0-4816-480b-9b93-a9eac4d9c89a
[UpdateElementConnectPointState] 🔍 开始更新连接点状态:
  元素类型: SFCBranchViewModel
  连接点索引: 1
  是否输入: False
  连接ID: 011cdf46-8785-493c-9249-5d6869517485
[UpdateElementConnectPointState] 🎯 找到分支元素适配器: c4b8d342-386c-46dd-aa77-0248eff64fcd, 分支类型: Selection, 适配器数量: 4
[UpdateElementConnectPointState] ⚠️ 精确匹配失败，使用Direction匹配: Direction=Output, Index=1
[UpdateElementConnectPointState] ✅ 连接点状态更新成功: SFCBranchViewModel, Direction=Output, Index=1, AdapterIndex=2, ConnectionId: 011cdf46-8785-493c-9249-5d6869517485
[UpdateElementConnectPointState] 🔍 开始更新连接点状态:
  元素类型: SFCTransitionViewModel
  连接点索引: 0
  是否输入: True
  连接ID: 011cdf46-8785-493c-9249-5d6869517485
[UpdateElementConnectPointState] ✅ 连接点状态更新成功: SFCTransitionViewModel, Direction=Input, Index=0, AdapterIndex=0, ConnectionId: 011cdf46-8785-493c-9249-5d6869517485
[UpdateConnectPointStates] 连接点状态更新完成: c4b8d342-386c-46dd-aa77-0248eff64fcd[1] -> 39f7ddb0-4816-480b-9b93-a9eac4d9c89a[0]
[连接点重叠检测] 点1: (258.5, 354.5), 点2: (250.5, 354.5), 距离: 8.0px, 重叠: True
[CreateConnectionPath] 连接 011cdf46-8785-493c-9249-5d6869517485 连接点重叠，隐藏连接线
[CreateConnectionPath] 起点: (258.5, 354.5), 终点: (250.5, 354.5)
连接 011cdf46-8785-493c-9249-5d6869517485 连接点重叠，使用连接点重叠方式
[AddConnection] 延迟更新连接线 011cdf46-8785-493c-9249-5d6869517485 的路径点
[CalculateElementConnectPoint] ✅ 选择分支ViewModel左下连接点(索引1): 250,354.5
[AddConnection] 延迟更新 - 源对象类型: SFCBranchViewModel, 目标对象类型: SFCTransitionViewModel
[AddConnection] 延迟更新 - 源位置: 229.5,319.5, 目标位置: 190.2,354.5
[AddConnection] 延迟更新后的路径点: 250,354.5 -> 250.5,354.5
[AddConnection] PathPoints集合已更新，应该触发重新评估
[InsertStepAfterSelected] 选中元素: 转换2
[InsertStepAfterSelected] 选中元素位置: 190.2,354.5
[GetActualConnectPointPosition] 转换条件连接点: 索引0, 位置250.5,384.5
[CalculateConnectPointAlignedPositionDynamic] 精确对齐计算:
  源元素类型: SFCTransitionViewModel
  源连接点: 250.5,384.5
  目标尺寸: 100,128
  目标输入偏移: 50.5,10
  计算位置: 200,374.5
[InsertStepAfterSelected] 新步骤位置: 200,374.5
[InsertStepAfterSelected] 新步骤创建: 6a190446-7313-4c26-a6df-a1ba6dc654e0
[SFCCanvas] 为新创建的步骤添加位置变化监听: 6a190446-7313-4c26-a6df-a1ba6dc654e0
[InsertStepAfterSelected] 步骤已添加到集合
[InsertStepAfterSelected] 准备创建连接: 39f7ddb0-4816-480b-9b93-a9eac4d9c89a -> 6a190446-7313-4c26-a6df-a1ba6dc654e0
[InsertStepAfterSelected] 开始创建连接: 39f7ddb0-4816-480b-9b93-a9eac4d9c89a -> 6a190446-7313-4c26-a6df-a1ba6dc654e0, 源索引: 0
[AddConnection] 开始执行: 39f7ddb0-4816-480b-9b93-a9eac4d9c89a -> 6a190446-7313-4c26-a6df-a1ba6dc654e0
[AddConnection] 对象查找结果: sourceObject=SFCTransitionModel, targetObject=SFCStepModel
[AddConnection] 位置获取: 源ViewModel位置=190.2,354.5, 源Model位置=190.2,354.5
[AddConnection] 位置获取: 目标ViewModel位置=200,374.5, 目标Model位置=200,374.5
[GetPreciseConnectPointPosition] ⚠️ 回退到计算方法
[GetPreciseConnectPointPosition] ⚠️ 回退到计算方法
[AddConnection] 创建连接: 39f7ddb0-4816-480b-9b93-a9eac4d9c89a -> 6a190446-7313-4c26-a6df-a1ba6dc654e0
[AddConnection] 源位置: 190.2,354.5, 目标位置: 200,374.5
[AddConnection] 源对象类型: SFCTransitionModel, 目标对象类型: SFCStepModel
[AddConnection] 源连接点: 250.5,384.5, 目标连接点: 250,380.5
[AddConnection] 连接创建完成，路径点数量: 2
[AddConnectionViewModel] 连接 ef04e7c4-71c8-4db2-8de0-336d99fc5c97 路径点数量: 2
[AddConnectionViewModel] ViewModel路径点数量: 2
[AddConnectionViewModel] 起点: 250.5,384.5, 终点: 250,380.5
[AddConnection] 🔄 开始更新连接点状态: 39f7ddb0-4816-480b-9b93-a9eac4d9c89a -> 6a190446-7313-4c26-a6df-a1ba6dc654e0
[UpdateElementConnectPointState] 🔍 开始更新连接点状态:
  元素类型: SFCTransitionViewModel
  连接点索引: 0
  是否输入: False
  连接ID: ef04e7c4-71c8-4db2-8de0-336d99fc5c97
[UpdateElementConnectPointState] ✅ 连接点状态更新成功: SFCTransitionViewModel, Direction=Output, Index=0, AdapterIndex=0, ConnectionId: ef04e7c4-71c8-4db2-8de0-336d99fc5c97
[UpdateElementConnectPointState] 🔍 开始更新连接点状态:
  元素类型: SFCStepViewModel
  连接点索引: 0
  是否输入: True
  连接ID: ef04e7c4-71c8-4db2-8de0-336d99fc5c97
[UpdateElementConnectPointState] ✅ 连接点状态更新成功: SFCStepViewModel, Direction=Input, Index=0, AdapterIndex=0, ConnectionId: ef04e7c4-71c8-4db2-8de0-336d99fc5c97
[UpdateConnectPointStates] 连接点状态更新完成: 39f7ddb0-4816-480b-9b93-a9eac4d9c89a[0] -> 6a190446-7313-4c26-a6df-a1ba6dc654e0[0]
[InsertStepAfterSelected] ✅ 连接创建成功: ef04e7c4-71c8-4db2-8de0-336d99fc5c97
[连接点重叠检测] 点1: (250.5, 384.5), 点2: (250.0, 380.5), 距离: 4.0px, 重叠: True
[CreateConnectionPath] 连接 ef04e7c4-71c8-4db2-8de0-336d99fc5c97 连接点重叠，隐藏连接线
[CreateConnectionPath] 起点: (250.5, 384.5), 终点: (250.0, 380.5)
连接 ef04e7c4-71c8-4db2-8de0-336d99fc5c97 连接点重叠，使用连接点重叠方式
[AddConnection] 延迟更新连接线 ef04e7c4-71c8-4db2-8de0-336d99fc5c97 的路径点
[AddConnection] 延迟更新 - 源对象类型: SFCTransitionViewModel, 目标对象类型: SFCStepViewModel
[AddConnection] 延迟更新 - 源位置: 190.2,354.5, 目标位置: 200,374.5
[AddConnection] 延迟更新后的路径点: 250.5,384.5 -> 250,380.5
[AddConnection] PathPoints集合已更新，应该触发重新评估
[InsertSelectionBranchAfterStep] 选中元素: 步骤2
[选择分支] 首次插入，现有分支: 0个
[GetActualConnectPointPosition] 步骤连接点: 索引0, 位置250,496.5
[CalculateConnectPointAlignedPositionForBranch] Selection分支精确对齐:
  源连接点: 250,496.5
  目标连接点偏移: 21,7
  计算位置: 229.5,494
[选择分支] 使用连接点精确对齐计算位置: 229.5,494
[选择分支] 新分支位置: 229.5,494
[选择分支] 创建: Initial, 位置: 229.5,494
[分支集合] 添加分支，位置: 229.5,494
[SFCBranchViewModel] 初始化适配器: BranchType=Selection, ElementType=SelectionBranch
[SFCBranchViewModel] ✅ 选择分支适配器配置完成，数量: 4
[SFCBranchViewModel] 🔧 索引1适配器类型: Input (修复为Input)
[SFCBranchViewModel] 🔧 索引2适配器类型: Output (修复为Output)
[SFCBranchViewModel] 🔧 临时修复：为选择分支设置默认转换条件编号 T1
[SFCBranchViewModel] 动态布局属性初始化:  -> 147x80
[SFCBranchViewModel] 🔍 关键属性值调试:
  - LeftLineX1: 20, LeftLineX2: 20
  - LeftLineLeft: 1
[MainHorizontalLineX2] 超级简化: 宽度147 → 线条长度147 → X2=44.0
  - MainHorizontalLineX1: -103, MainHorizontalLineX2: 44
  - MainHorizontalLineLeft: 124
  - ViewType: Initial, IsSelected: False
  - InternalTransitionNumber: 1, InternalTransitionHasLabel: True
[SFCBranchViewModel] ⚠️ 无法获取EnhancedSFCViewModel实例
[分支集合] ViewModel位置: 229.5,494
[SFCCanvas] 为新创建的分支添加位置变化监听: cf743511-2b58-48ec-b7b8-d943fadf727c
[分支闭合管理器] 注册分支: cf743511-2b58-48ec-b7b8-d943fadf727c, 初始路数: 1
[嵌套检测] 开始查找分支 cf743511-2b58-48ec-b7b8-d943fadf727c 的父分支
[嵌套检测] 找到 0 个输入连接
[空间检测] 开始基于空间关系查找父分支（就近原则）
[空间检测] 找到 1 个现有分支
[空间检测] 检查分支 c4b8d342-386c-46dd-aa77-0248eff64fcd, 位置: 229.5,319.5
[空间检测] 垂直距离: 174.5, 水平距离: 0
[空间检测] 空间关系检查通过
[空间检测] 候选父分支: c4b8d342-386c-46dd-aa77-0248eff64fcd, 垂直距离: 174.5
[空间检测] 就近原则选择父分支: c4b8d342-386c-46dd-aa77-0248eff64fcd, 距离: 174.5
[空间检测] 其他候选分支: 
[嵌套检测] 通过空间关系找到父分支: c4b8d342-386c-46dd-aa77-0248eff64fcd
[EnhancedSFCViewModel] 获取所有元素: 步骤=2, 转换=1, 分支=2, 连接=3, 动作=0
[路数计算] 选择分支 c4b8d342-386c-46dd-aa77-0248eff64fcd: 出口连接数=1, 路数=1
[分支闭合管理器] 重新计算实际路数: c4b8d342-386c-46dd-aa77-0248eff64fcd = 1路
[分支闭合管理器] 宽度计算详情: c4b8d342-386c-46dd-aa77-0248eff64fcd
  - 旧路数: 1, 新路数: 2
  - 旧宽度: 185px, 新宽度: 370px
  - 嵌套分支数量: 1
[分支闭合管理器] 分支已存在，跳过注册: cf743511-2b58-48ec-b7b8-d943fadf727c
[简化宽度计算器] 查找受影响的父分支: c4b8d342-386c-46dd-aa77-0248eff64fcd
[简化宽度计算器] 缓存清理完成: c4b8d342-386c-46dd-aa77-0248eff64fcd, 受影响父分支数: 0
[SFCCanvas] 位置变化: SFCBranchViewModel.DynamicWidth
[SFCCanvas] 位置变化: SFCBranchViewModel.MainHorizontalLineX2
[MainHorizontalLineX2] 超级简化: 宽度370 → 线条长度370 → X2=267.0
[SFCCanvas] 位置变化: SFCBranchViewModel.RightTopConnectPointLeft
[MainHorizontalLineX2] 超级简化: 宽度370 → 线条长度370 → X2=267.0
[RightTopConnectPointLeft] 主横线终点=391.0, 右上连接点位置=385.0
[SFCCanvas] 位置变化: SFCBranchViewModel.RightBottomConnectPointLeft
[MainHorizontalLineX2] 超级简化: 宽度370 → 线条长度370 → X2=267.0
[RightBottomConnectPointLeft] 主横线终点=391.0, 右下连接点位置=385.0
[SFCCanvas] 位置变化: SFCBranchViewModel.RightConnectionLineLeft
[MainHorizontalLineX2] 超级简化: 宽度370 → 线条长度370 → X2=267.0
[RightConnectionLineLeft] 主横线终点=391.0, 右侧竖线位置=388.5
[SFCCanvas] 位置变化: SFCBranchViewModel.TransitionLineLeft
[SFCCanvas] 位置变化: SFCBranchViewModel.TransitionIdentifierLeft
[MainHorizontalLineX2] 超级简化: 宽度370 → 线条长度370 → X2=267.0
[TransitionIdentifierLeft] 主横线终点=391.0, T1位置=399.0
[SFCCanvas] 位置变化: SFCBranchViewModel.TransitionConnectionLineLeft
[MainHorizontalLineX2] 超级简化: 宽度370 → 线条长度370 → X2=267.0
[TransitionConnectionLineLeft] 主横线终点=391.0, 连接虚线位置=330.0
[SFCCanvas] 位置变化: SFCBranchViewModel.TransitionCrossLineLeft
[MainHorizontalLineX2] 超级简化: 宽度370 → 线条长度370 → X2=267.0
[TransitionCrossLineLeft] 主横线终点=391.0, 十字横线位置=330.0
[SFCCanvas] 位置变化: SFCBranchViewModel.TransitionContactLeftLineLeft
[MainHorizontalLineX2] 超级简化: 宽度370 → 线条长度370 → X2=267.0
[TransitionContactLeftLineLeft] 主横线终点=391.0, 触点左竖线位置=351.0
[SFCCanvas] 位置变化: SFCBranchViewModel.TransitionContactRightLineLeft
[MainHorizontalLineX2] 超级简化: 宽度370 → 线条长度370 → X2=267.0
[TransitionContactRightLineLeft] 主横线终点=391.0, 触点右竖线位置=358.0
[SFCCanvas] 位置变化: SFCBranchViewModel.RightInteractionLayerWidth
[SFCCanvas] 位置变化: SFCBranchViewModel.ParallelBranchWidth
[SFCCanvas] 位置变化: SFCBranchViewModel.ParallelRightVerticalLeft
[SFCCanvas] 位置变化: SFCBranchViewModel.ParallelRightConnectPointLeft
[SFCCanvas] 位置变化: SFCBranchViewModel.ParallelInteractionLayerWidth
[SFCCanvas] 位置变化: SFCBranchViewModel.Size
[EnhancedSFCViewModel] 分支宽度已调整: c4b8d342-386c-46dd-aa77-0248eff64fcd 185px -> 370px
[简化宽度计算器] 宽度调整: c4b8d342-386c-46dd-aa77-0248eff64fcd 185px -> 370px
[简化宽度计算器] 嵌套分支插入，清除缓存: c4b8d342-386c-46dd-aa77-0248eff64fcd, cf743511-2b58-48ec-b7b8-d943fadf727c
[EnhancedSFCViewModel] 嵌套分支插入: c4b8d342-386c-46dd-aa77-0248eff64fcd -> cf743511-2b58-48ec-b7b8-d943fadf727c
[分支闭合管理器] 嵌套分支插入成功: c4b8d342-386c-46dd-aa77-0248eff64fcd -> cf743511-2b58-48ec-b7b8-d943fadf727c, 新宽度: 370px
[EnhancedSFCViewModel] 成功检测并处理嵌套分支插入: c4b8d342-386c-46dd-aa77-0248eff64fcd -> cf743511-2b58-48ec-b7b8-d943fadf727c
[AddConnection] 开始执行: 6a190446-7313-4c26-a6df-a1ba6dc654e0 -> cf743511-2b58-48ec-b7b8-d943fadf727c
[AddConnection] 对象查找结果: sourceObject=SFCStepModel, targetObject=SFCBranchModel
[AddConnection] 位置获取: 源ViewModel位置=200,374.5, 源Model位置=200.2,374.5
[AddConnection] 位置获取: 目标ViewModel位置=229.5,494, 目标Model位置=229.5,494
[GetPreciseConnectPointPosition] ⚠️ 回退到计算方法
[GetPreciseConnectPointPosition] ⚠️ 回退到计算方法
[CalculateElementConnectPoint] SFCBranchModel: 类型=Selection, 位置=229.5,494, 输出点=False
[对比调试] 如果是步骤输入点，位置应该是: 279.5,500
[CalculateElementConnectPoint] ✅ 选择分支左上连接点(索引0): 251,505.5
[AddConnection] 创建连接: 6a190446-7313-4c26-a6df-a1ba6dc654e0 -> cf743511-2b58-48ec-b7b8-d943fadf727c
[AddConnection] 源位置: 200,374.5, 目标位置: 229.5,494
[AddConnection] 源对象类型: SFCStepModel, 目标对象类型: SFCBranchModel
[AddConnection] 源连接点: 250,496.5, 目标连接点: 251,505.5
[AddConnection] 连接创建完成，路径点数量: 2
[AddConnectionViewModel] 连接 1f71e3f3-e8d9-42cb-8c4a-8355642159bd 路径点数量: 2
[AddConnectionViewModel] ViewModel路径点数量: 2
[AddConnectionViewModel] 起点: 250,496.5, 终点: 251,505.5
[AddConnection] 🔄 开始更新连接点状态: 6a190446-7313-4c26-a6df-a1ba6dc654e0 -> cf743511-2b58-48ec-b7b8-d943fadf727c
[UpdateElementConnectPointState] 🔍 开始更新连接点状态:
  元素类型: SFCStepViewModel
  连接点索引: 0
  是否输入: False
  连接ID: 1f71e3f3-e8d9-42cb-8c4a-8355642159bd
[UpdateElementConnectPointState] ✅ 连接点状态更新成功: SFCStepViewModel, Direction=Output, Index=0, AdapterIndex=0, ConnectionId: 1f71e3f3-e8d9-42cb-8c4a-8355642159bd
[UpdateElementConnectPointState] 🔍 开始更新连接点状态:
  元素类型: SFCBranchViewModel
  连接点索引: 0
  是否输入: True
  连接ID: 1f71e3f3-e8d9-42cb-8c4a-8355642159bd
[UpdateElementConnectPointState] 🎯 找到分支元素适配器: cf743511-2b58-48ec-b7b8-d943fadf727c, 分支类型: Selection, 适配器数量: 4
[UpdateElementConnectPointState] ✅ 连接点状态更新成功: SFCBranchViewModel, Direction=Input, Index=0, AdapterIndex=0, ConnectionId: 1f71e3f3-e8d9-42cb-8c4a-8355642159bd
[UpdateConnectPointStates] 连接点状态更新完成: 6a190446-7313-4c26-a6df-a1ba6dc654e0[0] -> cf743511-2b58-48ec-b7b8-d943fadf727c[0]
[选择分支] 跳过垂直对齐，保持计算位置: 229.5,494
[选择分支] 左分支使用竖直连接线，无需创建转换条件
[选择分支] 右分支转换条件已内嵌在选择分支视图中，无需单独创建
System.Windows.Data Error: 40 : BindingExpression path error: 'Description' property not found on 'object' ''SFCBranchViewModel' (HashCode=15841586)'. BindingExpression:Path=SelectedElement.Description; DataItem='EnhancedSFCViewModel' (HashCode=34232944); target element is 'TextBlock' (Name=''); target property is 'Text' (type 'String')
[EnhancedSFCViewModel] 需要刷新连接线: e46fde75-6434-4c77-8ba9-825cd9f4fb1d
[EnhancedSFCViewModel] 需要刷新连接线: 011cdf46-8785-493c-9249-5d6869517485
[EnhancedSFCViewModel] 已刷新分支 c4b8d342-386c-46dd-aa77-0248eff64fcd 相关的 2 条连接线
[InitializeConnectPoints] 开始初始化连接点，ViewModel.Id: cf743511-2b58-48ec-b7b8-d943fadf727c
[InitializeConnectPoints] XAML连接点查找结果: LeftTop=True, LeftBottom=True, RightTop=True, RightBottom=True
[SFCSelectionBranchView] 开始设置连接点ElementId: cf743511-2b58-48ec-b7b8-d943fadf727c
[SFCSelectionBranchView] ✅ LeftTop连接点ElementId设置: cf743511-2b58-48ec-b7b8-d943fadf727c
[SFCSelectionBranchView] ✅ LeftBottom连接点ElementId设置: cf743511-2b58-48ec-b7b8-d943fadf727c
[SFCSelectionBranchView] ✅ RightTop连接点ElementId设置: cf743511-2b58-48ec-b7b8-d943fadf727c
[SFCSelectionBranchView] ✅ RightBottom连接点ElementId设置: cf743511-2b58-48ec-b7b8-d943fadf727c
[InitializeConnectPoints] LeftTop连接点ElementId: 'cf743511-2b58-48ec-b7b8-d943fadf727c', 期望: 'cf743511-2b58-48ec-b7b8-d943fadf727c'
[InitializeConnectPoints] ✅ 已修复LeftTop连接点ElementId: 'cf743511-2b58-48ec-b7b8-d943fadf727c'
[SFCSelectionBranchView] 适配器检查: Count=4, 期望>=4
[SFCSelectionBranchView] ✅ LeftTop适配器绑定: Direction=Input, Index=0
[SFCSelectionBranchView] ✅ LeftBottom适配器绑定: Direction=Input, Index=1
[SFCSelectionBranchView] ✅ RightTop适配器绑定: Direction=Output, Index=2
[SFCSelectionBranchView] ✅ RightBottom适配器绑定: Direction=Output, Index=3
[SFCSelectionBranchView] 开始同步现有连接状态: cf743511-2b58-48ec-b7b8-d943fadf727c
[SFCSelectionBranchView] 找到 1 个相关连接
[SFCSelectionBranchView] ✅ 同步输入连接: 1f71e3f3-e8d9-42cb-8c4a-8355642159bd, 索引: 0
[MainHorizontalLineX2] 超级简化: 宽度147 → 线条长度147 → X2=44.0
[MainHorizontalLineX2] 超级简化: 宽度147 → 线条长度147 → X2=44.0
[RightConnectionLineLeft] 主横线终点=168.0, 右侧竖线位置=165.5
[MainHorizontalLineX2] 超级简化: 宽度147 → 线条长度147 → X2=44.0
[TransitionConnectionLineLeft] 主横线终点=168.0, 连接虚线位置=107.0
[MainHorizontalLineX2] 超级简化: 宽度147 → 线条长度147 → X2=44.0
[TransitionCrossLineLeft] 主横线终点=168.0, 十字横线位置=107.0
[MainHorizontalLineX2] 超级简化: 宽度147 → 线条长度147 → X2=44.0
[TransitionContactLeftLineLeft] 主横线终点=168.0, 触点左竖线位置=128.0
[MainHorizontalLineX2] 超级简化: 宽度147 → 线条长度147 → X2=44.0
[TransitionContactRightLineLeft] 主横线终点=168.0, 触点右竖线位置=135.0
[MainHorizontalLineX2] 超级简化: 宽度147 → 线条长度147 → X2=44.0
[RightTopConnectPointLeft] 主横线终点=168.0, 右上连接点位置=162.0
[MainHorizontalLineX2] 超级简化: 宽度147 → 线条长度147 → X2=44.0
[RightBottomConnectPointLeft] 主横线终点=168.0, 右下连接点位置=162.0
[MainHorizontalLineX2] 超级简化: 宽度147 → 线条长度147 → X2=44.0
[TransitionIdentifierLeft] 主横线终点=168.0, T1位置=176.0
[InitializeConnectPoints] 连接点已初始化，跳过重复初始化: cf743511-2b58-48ec-b7b8-d943fadf727c
System.Windows.Data Error: 4 : Cannot find source for binding with reference 'RelativeSource FindAncestor, AncestorType='System.Windows.Controls.UserControl', AncestorLevel='1''. BindingExpression:Path=DataContext.InsertSelectionBranchFromAnyPartCommand; DataItem=null; target element is 'MenuItem' (Name=''); target property is 'Command' (type 'ICommand')
System.Windows.Data Error: 4 : Cannot find source for binding with reference 'RelativeSource FindAncestor, AncestorType='System.Windows.Controls.UserControl', AncestorLevel='1''. BindingExpression:Path=DataContext.InsertSiblingBranchCommand; DataItem=null; target element is 'MenuItem' (Name=''); target property is 'Command' (type 'ICommand')
System.Windows.Data Error: 4 : Cannot find source for binding with reference 'RelativeSource FindAncestor, AncestorType='System.Windows.Controls.UserControl', AncestorLevel='1''. BindingExpression:Path=DataContext.InsertParallelBranchFromAnyPartCommand; DataItem=null; target element is 'MenuItem' (Name=''); target property is 'Command' (type 'ICommand')
[连接点重叠检测] 点1: (250.0, 496.5), 点2: (251.0, 505.5), 距离: 9.1px, 重叠: True
[CreateConnectionPath] 连接 1f71e3f3-e8d9-42cb-8c4a-8355642159bd 连接点重叠，隐藏连接线
[CreateConnectionPath] 起点: (250.0, 496.5), 终点: (251.0, 505.5)
连接 1f71e3f3-e8d9-42cb-8c4a-8355642159bd 连接点重叠，使用连接点重叠方式
[AddConnection] 延迟更新连接线 1f71e3f3-e8d9-42cb-8c4a-8355642159bd 的路径点
[CalculateElementConnectPoint] ✅ 选择分支ViewModel左上连接点(索引0): 250,501
[AddConnection] 延迟更新 - 源对象类型: SFCStepViewModel, 目标对象类型: SFCBranchViewModel
[AddConnection] 延迟更新 - 源位置: 200,374.5, 目标位置: 229.5,494
[AddConnection] 延迟更新后的路径点: 250,496.5 -> 250,501
[AddConnection] PathPoints集合已更新，应该触发重新评估
[嵌套检测] 开始查找分支 cf743511-2b58-48ec-b7b8-d943fadf727c 的父分支
[嵌套检测] 找到 1 个输入连接
[嵌套检测] 源元素类型: SFCStepModel, ID: 6a190446-7313-4c26-a6df-a1ba6dc654e0
[嵌套检测] 查找包含步骤 6a190446-7313-4c26-a6df-a1ba6dc654e0 的分支
[嵌套检测] 步骤 6a190446-7313-4c26-a6df-a1ba6dc654e0 在分支 c4b8d342-386c-46dd-aa77-0248eff64fcd 的路径中
[嵌套检测] 通过步骤 6a190446-7313-4c26-a6df-a1ba6dc654e0 找到父分支: c4b8d342-386c-46dd-aa77-0248eff64fcd
[分支闭合管理器] 分支 cf743511-2b58-48ec-b7b8-d943fadf727c 已经是 c4b8d342-386c-46dd-aa77-0248eff64fcd 的嵌套分支，跳过重复插入
“PC_Control2.Demo.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.WindowsDesktop.App\8.0.17\Accessibility.dll”。模块已生成，不包含符号。
[SFCCanvas] 位置变化: SFCBranchViewModel.IsSelected
