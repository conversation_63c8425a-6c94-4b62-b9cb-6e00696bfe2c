using System;
using System.Collections.Generic;
using System.Collections.Concurrent;
using System.Linq;
using System.Threading;
using PC_Control2.Demo.Models;
using PC_Control2.Demo.Configuration;

namespace PC_Control2.Demo.Services
{
    /// <summary>
    /// 分支闭合状态枚举
    /// </summary>
    public enum BranchClosureState
    {
        /// <summary>
        /// 未闭合 - 可以插入嵌套子分支，宽度可动态调整
        /// </summary>
        Open,
        
        /// <summary>
        /// 已闭合 - 宽度固定，不再受嵌套插入影响
        /// </summary>
        Closed,
        
        /// <summary>
        /// 正在闭合 - 过渡状态，即将变为闭合
        /// </summary>
        Closing
    }

    /// <summary>
    /// 分支闭合信息
    /// </summary>
    public class BranchClosureInfo
    {
        /// <summary>
        /// 分支ID
        /// </summary>
        public string BranchId { get; set; } = string.Empty;

        /// <summary>
        /// 闭合状态
        /// </summary>
        public BranchClosureState State { get; set; } = BranchClosureState.Open;

        /// <summary>
        /// 闭合时的路数（宽度固定值）
        /// </summary>
        public int ClosedPathCount { get; set; } = 0;

        /// <summary>
        /// 闭合时的宽度（像素）
        /// </summary>
        public double ClosedWidth { get; set; } = 0.0;

        /// <summary>
        /// 闭合时间
        /// </summary>
        public DateTime? ClosedTime { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime LastModifiedTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 是否可以调整宽度
        /// </summary>
        public bool CanAdjustWidth => State == BranchClosureState.Open;

        /// <summary>
        /// 嵌套子分支列表（仅未闭合时维护）
        /// </summary>
        public HashSet<string> NestedBranches { get; set; } = new();
    }

    /// <summary>
    /// 分支闭合状态变化事件参数
    /// </summary>
    public class BranchClosureStateChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 分支ID
        /// </summary>
        public string BranchId { get; set; } = string.Empty;

        /// <summary>
        /// 原状态
        /// </summary>
        public BranchClosureState OldState { get; set; }

        /// <summary>
        /// 新状态
        /// </summary>
        public BranchClosureState NewState { get; set; }

        /// <summary>
        /// 闭合时的宽度
        /// </summary>
        public double? ClosedWidth { get; set; }

        /// <summary>
        /// 变化时间
        /// </summary>
        public DateTime ChangeTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// SFC分支闭合状态管理器
    /// 负责管理分支的闭合状态，区分已闭合和未闭合分支的行为
    /// </summary>
    public class SFCBranchClosureManager
    {
        #region 私有字段

        /// <summary>
        /// 分支闭合信息字典
        /// </summary>
        private readonly ConcurrentDictionary<string, BranchClosureInfo> _branchClosureInfo = new();

        /// <summary>
        /// 单位宽度常量
        /// </summary>
        private const double UNIT_WIDTH = 147.0;

        /// <summary>
        /// 线程安全锁
        /// </summary>
        private readonly ReaderWriterLockSlim _lock = new();

        /// <summary>
        /// 统计信息
        /// </summary>
        private long _totalBranches = 0;
        private long _closedBranches = 0;
        private long _openBranches = 0;

        #endregion

        #region 事件

        /// <summary>
        /// 分支闭合状态变化事件
        /// </summary>
        public event EventHandler<BranchClosureStateChangedEventArgs>? BranchClosureStateChanged;

        /// <summary>
        /// 分支宽度调整请求事件（仅未闭合分支触发）
        /// </summary>
        public event EventHandler<(string branchId, double oldWidth, double newWidth)>? WidthAdjustmentRequested;

        /// <summary>
        /// 嵌套分支插入事件
        /// </summary>
        public event EventHandler<(string parentBranchId, string nestedBranchId)>? NestedBranchInserted;

        /// <summary>
        /// 🔧 修复：获取所有元素的委托，用于重新计算路数
        /// </summary>
        public Func<List<SFCElementModelBase>>? GetAllElementsForCalculation;

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public SFCBranchClosureManager()
        {
            System.Diagnostics.Debug.WriteLine("[分支闭合管理器] 初始化完成");
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 注册分支（默认为未闭合状态）
        /// </summary>
        public void RegisterBranch(string branchId, int initialPathCount = 1)
        {
            if (string.IsNullOrEmpty(branchId))
                throw new ArgumentException("分支ID不能为空", nameof(branchId));

            _lock.EnterWriteLock();
            try
            {
                if (!_branchClosureInfo.ContainsKey(branchId))
                {
                    var closureInfo = new BranchClosureInfo
                    {
                        BranchId = branchId,
                        State = BranchClosureState.Open,
                        ClosedPathCount = initialPathCount,
                        ClosedWidth = initialPathCount * UNIT_WIDTH
                    };

                    _branchClosureInfo[branchId] = closureInfo;
                    Interlocked.Increment(ref _totalBranches);
                    Interlocked.Increment(ref _openBranches);

                    System.Diagnostics.Debug.WriteLine($"[分支闭合管理器] 注册分支: {branchId}, 初始路数: {initialPathCount}");
                }
            }
            finally
            {
                _lock.ExitWriteLock();
            }
        }

        /// <summary>
        /// 闭合分支
        /// </summary>
        public bool CloseBranch(string branchId)
        {
            if (string.IsNullOrEmpty(branchId))
                return false;

            _lock.EnterWriteLock();
            try
            {
                if (_branchClosureInfo.TryGetValue(branchId, out var closureInfo))
                {
                    if (closureInfo.State == BranchClosureState.Open)
                    {
                        var oldState = closureInfo.State;
                        closureInfo.State = BranchClosureState.Closed;
                        closureInfo.ClosedTime = DateTime.Now;
                        closureInfo.LastModifiedTime = DateTime.Now;

                        Interlocked.Decrement(ref _openBranches);
                        Interlocked.Increment(ref _closedBranches);

                        // 触发状态变化事件
                        BranchClosureStateChanged?.Invoke(this, new BranchClosureStateChangedEventArgs
                        {
                            BranchId = branchId,
                            OldState = oldState,
                            NewState = BranchClosureState.Closed,
                            ClosedWidth = closureInfo.ClosedWidth
                        });

                        System.Diagnostics.Debug.WriteLine($"[分支闭合管理器] 分支已闭合: {branchId}, 固定宽度: {closureInfo.ClosedWidth}px");
                        return true;
                    }
                }
                return false;
            }
            finally
            {
                _lock.ExitWriteLock();
            }
        }

        /// <summary>
        /// 内部注册分支方法（不获取锁，用于已在锁内的调用）
        /// </summary>
        private void RegisterBranchInternal(string branchId, int initialPathCount = 1)
        {
            if (string.IsNullOrEmpty(branchId))
                throw new ArgumentException("分支ID不能为空", nameof(branchId));

            if (!_branchClosureInfo.ContainsKey(branchId))
            {
                var info = new BranchClosureInfo
                {
                    BranchId = branchId,
                    State = BranchClosureState.Open,
                    ClosedPathCount = initialPathCount,
                    ClosedWidth = initialPathCount * UNIT_WIDTH,
                    CreatedTime = DateTime.Now,
                    LastModifiedTime = DateTime.Now,
                    NestedBranches = new HashSet<string>()
                };

                _branchClosureInfo[branchId] = info;
                System.Diagnostics.Debug.WriteLine($"[分支闭合管理器] 内部注册分支: {branchId}, 初始路数: {initialPathCount}");
            }
            else
            {
                System.Diagnostics.Debug.WriteLine($"[分支闭合管理器] 分支已存在，跳过注册: {branchId}");
            }
        }

        /// <summary>
        /// 尝试插入嵌套分支（只有未闭合分支才能成功）
        /// </summary>
        public bool TryInsertNestedBranch(string parentBranchId, string nestedBranchId, int nestedPathCount = 1)
        {
            if (string.IsNullOrEmpty(parentBranchId) || string.IsNullOrEmpty(nestedBranchId))
                return false;

            _lock.EnterWriteLock();
            try
            {
                if (_branchClosureInfo.TryGetValue(parentBranchId, out var parentInfo))
                {
                    // 只有未闭合的分支才能插入嵌套分支
                    if (parentInfo.State == BranchClosureState.Open)
                    {
                        // 检查是否已经是嵌套分支，避免重复插入
                        if (parentInfo.NestedBranches.Contains(nestedBranchId))
                        {
                            System.Diagnostics.Debug.WriteLine($"[分支闭合管理器] 分支 {nestedBranchId} 已经是 {parentBranchId} 的嵌套分支，跳过重复插入");
                            return false;
                        }

                        // 添加嵌套分支记录
                        parentInfo.NestedBranches.Add(nestedBranchId);
                        parentInfo.LastModifiedTime = DateTime.Now;

                        // 🔧 修复：重新计算实际路数，避免累加错误
                        var oldPathCount = parentInfo.ClosedPathCount;
                        var oldWidth = parentInfo.ClosedWidth;

                        // 重新计算实际路数而不是简单累加
                        var actualPathCount = CalculateActualPathCount(parentBranchId);
                        var newPathCount = Math.Max(actualPathCount, oldPathCount + nestedPathCount);
                        var newWidth = newPathCount * UNIT_WIDTH;

                        System.Diagnostics.Debug.WriteLine($"[分支闭合管理器] 宽度计算详情: {parentBranchId}");
                        System.Diagnostics.Debug.WriteLine($"  - 旧路数: {oldPathCount}, 新路数: {newPathCount}");
                        System.Diagnostics.Debug.WriteLine($"  - 旧宽度: {oldWidth}px, 新宽度: {newWidth}px");
                        System.Diagnostics.Debug.WriteLine($"  - 嵌套分支数量: {parentInfo.NestedBranches.Count}");

                        // 更新父分支宽度
                        parentInfo.ClosedPathCount = newPathCount;
                        parentInfo.ClosedWidth = newWidth;

                        // 注册嵌套分支（内部调用，不获取锁）
                        RegisterBranchInternal(nestedBranchId, nestedPathCount);

                        // 触发宽度调整请求事件，传递正确的旧宽度
                        WidthAdjustmentRequested?.Invoke(this, (parentBranchId, oldWidth, newWidth));

                        // 级联更新所有祖先分支
                        UpdateAncestorBranches(parentBranchId, nestedPathCount);

                        // 触发嵌套分支插入事件
                        NestedBranchInserted?.Invoke(this, (parentBranchId, nestedBranchId));

                        System.Diagnostics.Debug.WriteLine($"[分支闭合管理器] 嵌套分支插入成功: {parentBranchId} -> {nestedBranchId}, 新宽度: {newWidth}px");
                        return true;
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"[分支闭合管理器] 分支已闭合，无法插入嵌套分支: {parentBranchId}");
                        return false;
                    }
                }
                return false;
            }
            finally
            {
                _lock.ExitWriteLock();
            }
        }

        /// <summary>
        /// 扩展嵌套分支路数（只有未闭合的父分支才会触发调整）
        /// </summary>
        public bool ExpandNestedBranchPaths(string parentBranchId, string nestedBranchId, int additionalPaths)
        {
            if (string.IsNullOrEmpty(parentBranchId) || string.IsNullOrEmpty(nestedBranchId) || additionalPaths <= 0)
                return false;

            _lock.EnterWriteLock();
            try
            {
                if (_branchClosureInfo.TryGetValue(parentBranchId, out var parentInfo) &&
                    _branchClosureInfo.TryGetValue(nestedBranchId, out var nestedInfo))
                {
                    // 只有未闭合的父分支才会触发宽度调整
                    if (parentInfo.State == BranchClosureState.Open)
                    {
                        // 保存旧宽度
                        var oldParentWidth = parentInfo.ClosedWidth;

                        // 更新嵌套分支的路数
                        nestedInfo.ClosedPathCount += additionalPaths;
                        nestedInfo.ClosedWidth = nestedInfo.ClosedPathCount * UNIT_WIDTH;
                        nestedInfo.LastModifiedTime = DateTime.Now;

                        // 更新父分支的路数和宽度
                        parentInfo.ClosedPathCount += additionalPaths;
                        parentInfo.ClosedWidth = parentInfo.ClosedPathCount * UNIT_WIDTH;
                        parentInfo.LastModifiedTime = DateTime.Now;

                        // 触发宽度调整请求事件
                        WidthAdjustmentRequested?.Invoke(this, (parentBranchId, oldParentWidth, parentInfo.ClosedWidth));

                        System.Diagnostics.Debug.WriteLine($"[分支闭合管理器] 嵌套分支扩展: {nestedBranchId} +{additionalPaths}路, 父分支新宽度: {parentInfo.ClosedWidth}px");
                        return true;
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"[分支闭合管理器] 父分支已闭合，扩展不影响宽度: {parentBranchId}");
                        return false;
                    }
                }
                return false;
            }
            finally
            {
                _lock.ExitWriteLock();
            }
        }

        /// <summary>
        /// 检查分支是否可以调整宽度
        /// </summary>
        public bool CanAdjustBranchWidth(string branchId)
        {
            if (string.IsNullOrEmpty(branchId))
                return false;

            _lock.EnterReadLock();
            try
            {
                return _branchClosureInfo.TryGetValue(branchId, out var closureInfo) &&
                       closureInfo.CanAdjustWidth;
            }
            finally
            {
                _lock.ExitReadLock();
            }
        }

        /// <summary>
        /// 获取分支闭合状态
        /// </summary>
        public BranchClosureState GetBranchState(string branchId)
        {
            if (string.IsNullOrEmpty(branchId))
                return BranchClosureState.Closed; // 默认认为不存在的分支是闭合的

            _lock.EnterReadLock();
            try
            {
                return _branchClosureInfo.TryGetValue(branchId, out var closureInfo)
                    ? closureInfo.State
                    : BranchClosureState.Closed;
            }
            finally
            {
                _lock.ExitReadLock();
            }
        }

        /// <summary>
        /// 获取分支当前宽度
        /// </summary>
        public double GetBranchWidth(string branchId)
        {
            if (string.IsNullOrEmpty(branchId))
                return UNIT_WIDTH;

            // 检查当前线程是否已经持有写锁，如果是则直接查询，避免锁递归
            if (_lock.IsWriteLockHeld)
            {
                return _branchClosureInfo.TryGetValue(branchId, out var closureInfo)
                    ? closureInfo.ClosedWidth
                    : UNIT_WIDTH;
            }

            _lock.EnterReadLock();
            try
            {
                return _branchClosureInfo.TryGetValue(branchId, out var closureInfo)
                    ? closureInfo.ClosedWidth
                    : UNIT_WIDTH;
            }
            finally
            {
                _lock.ExitReadLock();
            }
        }

        /// <summary>
        /// 获取所有未闭合的分支
        /// </summary>
        public List<string> GetOpenBranches()
        {
            _lock.EnterReadLock();
            try
            {
                return _branchClosureInfo.Values
                    .Where(info => info.State == BranchClosureState.Open)
                    .Select(info => info.BranchId)
                    .ToList();
            }
            finally
            {
                _lock.ExitReadLock();
            }
        }

        /// <summary>
        /// 获取分支的详细信息
        /// </summary>
        public BranchClosureInfo? GetBranchInfo(string branchId)
        {
            if (string.IsNullOrEmpty(branchId))
                return null;

            _lock.EnterReadLock();
            try
            {
                return _branchClosureInfo.TryGetValue(branchId, out var info)
                    ? new BranchClosureInfo
                    {
                        BranchId = info.BranchId,
                        State = info.State,
                        ClosedPathCount = info.ClosedPathCount,
                        ClosedWidth = info.ClosedWidth,
                        ClosedTime = info.ClosedTime,
                        CreatedTime = info.CreatedTime,
                        LastModifiedTime = info.LastModifiedTime,
                        NestedBranches = new HashSet<string>(info.NestedBranches)
                    }
                    : null;
            }
            finally
            {
                _lock.ExitReadLock();
            }
        }

        /// <summary>
        /// 检查分支是否为嵌套分支
        /// </summary>
        public bool IsNestedBranch(string branchId)
        {
            if (string.IsNullOrEmpty(branchId))
                return false;

            // 检查当前线程是否已经持有写锁，如果是则直接查询，避免锁递归
            if (_lock.IsWriteLockHeld)
            {
                return _branchClosureInfo.Values.Any(info => info.NestedBranches.Contains(branchId));
            }

            _lock.EnterReadLock();
            try
            {
                // 检查是否有任何分支将此分支作为嵌套分支
                return _branchClosureInfo.Values.Any(info => info.NestedBranches.Contains(branchId));
            }
            finally
            {
                _lock.ExitReadLock();
            }
        }

        /// <summary>
        /// 查找分支的直接父分支（内部方法，假设已在锁内）
        /// </summary>
        private string? FindParentBranch(string branchId)
        {
            if (string.IsNullOrEmpty(branchId))
                return null;

            // 查找包含此分支作为嵌套分支的父分支
            // 注意：此方法假设调用者已经持有适当的锁
            foreach (var kvp in _branchClosureInfo)
            {
                if (kvp.Value.NestedBranches.Contains(branchId))
                {
                    return kvp.Key;
                }
            }
            return null;
        }

        /// <summary>
        /// 递归更新所有祖先分支的宽度
        /// </summary>
        private void UpdateAncestorBranches(string branchId, int pathCountIncrement)
        {
            var parentBranchId = FindParentBranch(branchId);
            if (string.IsNullOrEmpty(parentBranchId))
                return;

            if (_branchClosureInfo.TryGetValue(parentBranchId, out var parentInfo))
            {
                // 保存旧宽度
                var oldWidth = parentInfo.ClosedWidth;

                // 更新父分支的路数和宽度
                parentInfo.ClosedPathCount += pathCountIncrement;
                parentInfo.ClosedWidth = parentInfo.ClosedPathCount * UNIT_WIDTH;
                parentInfo.LastModifiedTime = DateTime.Now;

                System.Diagnostics.Debug.WriteLine($"[级联更新] 祖先分支 {parentBranchId} 宽度更新: {oldWidth}px -> {parentInfo.ClosedWidth}px (路数: {parentInfo.ClosedPathCount})");

                // 触发宽度调整请求事件
                WidthAdjustmentRequested?.Invoke(this, (parentBranchId, oldWidth, parentInfo.ClosedWidth));

                // 递归更新更上级的祖先分支
                UpdateAncestorBranches(parentBranchId, pathCountIncrement);
            }
        }

        /// <summary>
        /// 获取统计信息
        /// </summary>
        public (long total, long open, long closed) GetStatistics()
        {
            return (_totalBranches, _openBranches, _closedBranches);
        }

        /// <summary>
        /// 清除所有分支信息
        /// </summary>
        public void Clear()
        {
            _lock.EnterWriteLock();
            try
            {
                _branchClosureInfo.Clear();
                _totalBranches = 0;
                _openBranches = 0;
                _closedBranches = 0;
                System.Diagnostics.Debug.WriteLine("[分支闭合管理器] 清除所有分支信息");
            }
            finally
            {
                _lock.ExitWriteLock();
            }
        }

        /// <summary>
        /// 🔧 修复：计算分支的实际路数
        /// </summary>
        /// <param name="branchId">分支ID</param>
        /// <returns>实际路数</returns>
        private int CalculateActualPathCount(string branchId)
        {
            try
            {
                // 通过事件获取所有元素来重新计算路数
                var allElements = GetAllElementsForCalculation?.Invoke() ?? new List<SFCElementModelBase>();

                // 使用内容分析器重新计算
                var contentAnalyzer = new BranchContentAnalyzer();
                var actualPathCount = contentAnalyzer.GetBranchPathCount(branchId, allElements);

                System.Diagnostics.Debug.WriteLine($"[分支闭合管理器] 重新计算实际路数: {branchId} = {actualPathCount}路");
                return actualPathCount;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[分支闭合管理器] 计算实际路数失败: {branchId}, 错误: {ex.Message}");
                return 1; // 默认返回1路
            }
        }

        #endregion

        #region IDisposable

        public void Dispose()
        {
            _lock?.Dispose();
        }

        /// <summary>
        /// 获取嵌套分支的父分支ID
        /// </summary>
        public string? GetParentBranchId(string nestedBranchId)
        {
            _lock.EnterReadLock();
            try
            {
                foreach (var kvp in _branchClosureInfo)
                {
                    if (kvp.Value.NestedBranches.Contains(nestedBranchId))
                    {
                        return kvp.Key;
                    }
                }
                return null;
            }
            finally
            {
                _lock.ExitReadLock();
            }
        }

        /// <summary>
        /// 更新父分支宽度（用于子分支扩展时的母分支宽度调整）
        /// </summary>
        public void UpdateParentBranchWidth(string parentBranchId, int newPathCount)
        {
            _lock.EnterWriteLock();
            try
            {
                if (_branchClosureInfo.TryGetValue(parentBranchId, out var parentInfo))
                {
                    var oldWidth = parentInfo.ClosedWidth;
                    var newWidth = newPathCount * UNIT_WIDTH;

                    if (oldWidth != newWidth)
                    {
                        parentInfo.ClosedPathCount = newPathCount;
                        parentInfo.ClosedWidth = newWidth;
                        parentInfo.LastModifiedTime = DateTime.Now;

                        System.Diagnostics.Debug.WriteLine($"[分支闭合管理器] 母分支宽度更新: {parentBranchId} {oldWidth}px -> {newWidth}px (路数: {newPathCount})");

                        // 触发宽度调整请求事件
                        WidthAdjustmentRequested?.Invoke(this, (parentBranchId, oldWidth, newWidth));

                        // 级联更新祖先分支
                        int oldPathCount = (int)(oldWidth / UNIT_WIDTH);
                        UpdateAncestorBranches(parentBranchId, newPathCount - oldPathCount);
                    }
                }
            }
            finally
            {
                _lock.ExitWriteLock();
            }
        }

        #endregion
    }
}
