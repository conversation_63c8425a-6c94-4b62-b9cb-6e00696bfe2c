using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using PC_Control2.Demo.Models;
using PC_Control2.Demo.ViewModels;
using PC_Control2.Demo.Configuration;

namespace PC_Control2.Demo.Services
{
    /// <summary>
    /// 动态连接点对齐配置
    /// </summary>
    public class DynamicConnectPointAlignConfig
    {
        public Size ElementSize { get; set; }
        public SFCElementType ElementType { get; set; }
        public Point InputConnectPointOffset { get; set; }
        public Point OutputConnectPointOffset { get; set; }
        public string Description { get; set; } = string.Empty;
    }

    /// <summary>
    /// SFC动态连接点精确对齐器
    /// 集成现有的连接点精确对齐系统，支持动态布局下的连接点对齐
    /// </summary>
    public class SFCDynamicConnectPointAligner
    {
        #region 私有字段

        private readonly Dictionary<string, DynamicConnectPointAlignConfig> _alignConfigs = new();
        private readonly Dictionary<string, Point> _connectPointCache = new();
        private readonly object _cacheLock = new();

        #endregion

        #region 构造函数

        public SFCDynamicConnectPointAligner()
        {
            InitializeAlignConfigs();
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 计算动态布局下的连接点对齐位置
        /// </summary>
        /// <param name="sourceElement">源元素</param>
        /// <param name="targetSize">目标元素尺寸</param>
        /// <param name="connectPointIndex">连接点索引</param>
        /// <returns>对齐后的位置</returns>
        public Point CalculateDynamicAlignedPosition(object sourceElement, Size targetSize, int connectPointIndex = 0)
        {
            try
            {
                // 获取源元素的输出连接点位置
                var sourceConnectPoint = GetDynamicConnectPointPosition(sourceElement, connectPointIndex, true);
                
                if (sourceConnectPoint.HasValue)
                {
                    // 获取目标元素的输入连接点偏移
                    var targetInputOffset = GetDynamicInputConnectPointOffset(targetSize, sourceElement, connectPointIndex);

                    // 计算对齐位置
                    var alignedPosition = new Point(
                        sourceConnectPoint.Value.X - targetInputOffset.X,
                        sourceConnectPoint.Value.Y - targetInputOffset.Y
                    );

                    System.Diagnostics.Debug.WriteLine($"[SFCDynamicConnectPointAligner] 动态对齐计算完成: {alignedPosition}");
                    return alignedPosition;
                }
                else
                {
                    // 回退到基于元素位置的计算
                    return CalculateFallbackAlignedPosition(sourceElement, targetSize);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[SFCDynamicConnectPointAligner] 计算对齐位置时发生异常: {ex.Message}");
                return CalculateFallbackAlignedPosition(sourceElement, targetSize);
            }
        }

        /// <summary>
        /// 更新动态分支的连接点位置
        /// </summary>
        /// <param name="branchViewModel">分支视图模型</param>
        /// <param name="newSize">新尺寸</param>
        public void UpdateDynamicBranchConnectPoints(SFCBranchViewModel branchViewModel, Size newSize)
        {
            if (branchViewModel == null) return;

            // 更新分支的动态尺寸
            branchViewModel.UpdateDynamicSize(newSize.Width, newSize.Height);

            // 清除相关的连接点缓存
            ClearConnectPointCache(branchViewModel.Id);

            System.Diagnostics.Debug.WriteLine($"[SFCDynamicConnectPointAligner] 分支连接点更新完成: {branchViewModel.Id} -> {newSize}");
        }

        /// <summary>
        /// 验证连接点对齐精度
        /// </summary>
        /// <param name="sourceElement">源元素</param>
        /// <param name="targetElement">目标元素</param>
        /// <param name="connectPointIndex">连接点索引</param>
        /// <returns>对齐精度（像素）</returns>
        public double ValidateAlignmentPrecision(object sourceElement, object targetElement, int connectPointIndex = 0)
        {
            var sourceConnectPoint = GetDynamicConnectPointPosition(sourceElement, connectPointIndex, true);
            var targetConnectPoint = GetDynamicConnectPointPosition(targetElement, 0, false);

            if (sourceConnectPoint.HasValue && targetConnectPoint.HasValue)
            {
                var distance = Math.Sqrt(
                    Math.Pow(sourceConnectPoint.Value.X - targetConnectPoint.Value.X, 2) +
                    Math.Pow(sourceConnectPoint.Value.Y - targetConnectPoint.Value.Y, 2)
                );

                System.Diagnostics.Debug.WriteLine($"[SFCDynamicConnectPointAligner] 对齐精度验证: {distance:F2}px");
                return distance;
            }

            return double.MaxValue; // 无法验证时返回最大值
        }

        /// <summary>
        /// 清除连接点缓存
        /// </summary>
        /// <param name="elementId">元素ID，如果为null则清除所有缓存</param>
        public void ClearConnectPointCache(string? elementId = null)
        {
            lock (_cacheLock)
            {
                if (elementId == null)
                {
                    _connectPointCache.Clear();
                }
                else
                {
                    var keysToRemove = _connectPointCache.Keys
                        .Where(key => key.StartsWith(elementId))
                        .ToList();
                    
                    foreach (var key in keysToRemove)
                    {
                        _connectPointCache.Remove(key);
                    }
                }
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化对齐配置
        /// </summary>
        private void InitializeAlignConfigs()
        {
            // 转换条件配置
            _alignConfigs["Transition"] = new DynamicConnectPointAlignConfig
            {
                ElementSize = new Size(74, 30),
                ElementType = SFCElementType.Transition,
                InputConnectPointOffset = new Point(55.3 + 5 - 0.5, -5 + 5 - 4.5),
                OutputConnectPointOffset = new Point(55.3 + 5, 25 + 5),
                Description = "转换条件元素"
            };

            // 步骤配置
            _alignConfigs["Step"] = new DynamicConnectPointAlignConfig
            {
                ElementSize = new Size(100, 120),
                ElementType = SFCElementType.Step,
                InputConnectPointOffset = new Point(45 + 5 + 0.5, 1 + 5 + 4),
                OutputConnectPointOffset = new Point(45 + 5, 115 + 5),
                Description = "步骤元素"
            };

            // 选择分支配置（动态尺寸）
            _alignConfigs["SelectionBranch"] = new DynamicConnectPointAlignConfig
            {
                ElementSize = new Size(147, 80), // 默认尺寸，实际使用动态尺寸
                ElementType = SFCElementType.SelectionBranch,
                InputConnectPointOffset = new Point(16 + 5, 2 + 5),
                OutputConnectPointOffset = new Point(162 + 5, 61 + 5),
                Description = "选择分支元素（动态尺寸）"
            };

            // 并行分支配置（动态尺寸）
            _alignConfigs["ParallelBranch"] = new DynamicConnectPointAlignConfig
            {
                ElementSize = new Size(200, 50), // 默认尺寸，实际使用动态尺寸
                ElementType = SFCElementType.ParallelBranch,
                InputConnectPointOffset = new Point(37 + 5, -9 + 5),
                OutputConnectPointOffset = new Point(183 + 5, 19.5 + 5),
                Description = "并行分支元素（动态尺寸）"
            };

            // 终止元素配置
            _alignConfigs["Termination"] = new DynamicConnectPointAlignConfig
            {
                ElementSize = new Size(50, 50),
                ElementType = SFCElementType.Termination,
                InputConnectPointOffset = new Point(20 + 5, 6 + 5),
                OutputConnectPointOffset = new Point(20 + 5, 44 + 5),
                Description = "终止元素"
            };

            // 跳转元素配置
            _alignConfigs["Jump"] = new DynamicConnectPointAlignConfig
            {
                ElementSize = new Size(60, 50),
                ElementType = SFCElementType.Jump,
                InputConnectPointOffset = new Point(25 + 5 + 0.5, 6 + 5),
                OutputConnectPointOffset = new Point(25 + 5, 44 + 5),
                Description = "跳转元素"
            };
        }

        /// <summary>
        /// 获取动态连接点位置
        /// </summary>
        /// <param name="element">元素</param>
        /// <param name="connectPointIndex">连接点索引</param>
        /// <param name="isOutput">是否为输出连接点</param>
        /// <returns>连接点位置</returns>
        private Point? GetDynamicConnectPointPosition(object element, int connectPointIndex, bool isOutput)
        {
            var cacheKey = $"{GetElementId(element)}_{connectPointIndex}_{isOutput}";
            
            lock (_cacheLock)
            {
                if (_connectPointCache.TryGetValue(cacheKey, out var cachedPoint))
                {
                    return cachedPoint;
                }
            }

            Point? connectPoint = null;

            // 根据元素类型获取连接点位置
            if (element is SFCBranchViewModel branchVM)
            {
                connectPoint = GetBranchConnectPointPosition(branchVM, connectPointIndex, isOutput);
            }
            else
            {
                // 其他元素类型的处理
                connectPoint = GetGenericConnectPointPosition(element, connectPointIndex, isOutput);
            }

            // 缓存结果
            if (connectPoint.HasValue)
            {
                lock (_cacheLock)
                {
                    _connectPointCache[cacheKey] = connectPoint.Value;
                }
            }

            return connectPoint;
        }

        /// <summary>
        /// 获取分支连接点位置
        /// </summary>
        /// <param name="branchVM">分支视图模型</param>
        /// <param name="connectPointIndex">连接点索引</param>
        /// <param name="isOutput">是否为输出连接点</param>
        /// <returns>连接点位置</returns>
        private Point? GetBranchConnectPointPosition(SFCBranchViewModel branchVM, int connectPointIndex, bool isOutput)
        {
            var elementPosition = branchVM.Position;

            if (branchVM.BranchType == SFCBranchType.Selection)
            {
                // 选择分支的连接点位置（使用硬编码的Y坐标）
                return connectPointIndex switch
                {
                    0 => new Point(elementPosition.X + branchVM.LeftTopConnectPointLeft + 5,
                                  elementPosition.Y + 2 + 5), // Y坐标硬编码
                    1 => new Point(elementPosition.X + branchVM.LeftBottomConnectPointLeft + 5,
                                  elementPosition.Y + 30 + 5), // Y坐标硬编码
                    2 => new Point(elementPosition.X + branchVM.RightTopConnectPointLeft + 5,
                                  elementPosition.Y + 30 + 5), // Y坐标硬编码
                    3 => new Point(elementPosition.X + branchVM.RightBottomConnectPointLeft + 5,
                                  elementPosition.Y + 61 + 5), // Y坐标硬编码
                    _ => null
                };
            }
            else if (branchVM.BranchType == SFCBranchType.Parallel)
            {
                // 并行分支的连接点位置（使用硬编码的Y坐标）
                return connectPointIndex switch
                {
                    0 => new Point(elementPosition.X + 37 + 5,
                                  elementPosition.Y + (-9) + 5), // 左侧上端连接点，硬编码
                    1 => new Point(elementPosition.X + 37 + 5,
                                  elementPosition.Y + 19.5 + 5), // 左侧双线连接点，硬编码
                    2 => new Point(elementPosition.X + branchVM.ParallelRightConnectPointLeft + 5,
                                  elementPosition.Y + 19.5 + 5), // 右侧连接点，X动态Y硬编码
                    _ => null
                };
            }

            return null;
        }

        /// <summary>
        /// 获取通用连接点位置
        /// </summary>
        /// <param name="element">元素</param>
        /// <param name="connectPointIndex">连接点索引</param>
        /// <param name="isOutput">是否为输出连接点</param>
        /// <returns>连接点位置</returns>
        private Point? GetGenericConnectPointPosition(object element, int connectPointIndex, bool isOutput)
        {
            // 这里可以实现其他元素类型的连接点位置计算
            // 暂时返回null，表示使用回退方案
            return null;
        }

        /// <summary>
        /// 获取动态输入连接点偏移
        /// </summary>
        /// <param name="targetSize">目标尺寸</param>
        /// <param name="sourceElement">源元素</param>
        /// <param name="connectPointIndex">连接点索引</param>
        /// <returns>输入连接点偏移</returns>
        private Point GetDynamicInputConnectPointOffset(Size targetSize, object sourceElement, int connectPointIndex)
        {
            // 根据目标尺寸查找匹配的配置
            var config = _alignConfigs.Values.FirstOrDefault(c => 
                Math.Abs(c.ElementSize.Width - targetSize.Width) < 1 && 
                Math.Abs(c.ElementSize.Height - targetSize.Height) < 1);

            if (config != null)
            {
                // 特殊处理：选择分支右侧下方插入步骤的情况
                if (sourceElement is SFCBranchViewModel branchVM &&
                    branchVM.BranchType == SFCBranchType.Selection &&
                    connectPointIndex == 3 &&
                    config.ElementType == SFCElementType.Step)
                {
                    return new Point(45 + 5 - 0.5, 1 + 5 + 4);
                }

                return config.InputConnectPointOffset;
            }

            // 默认使用几何中心
            return new Point(targetSize.Width / 2, targetSize.Height / 2);
        }

        /// <summary>
        /// 计算回退对齐位置
        /// </summary>
        /// <param name="sourceElement">源元素</param>
        /// <param name="targetSize">目标尺寸</param>
        /// <returns>回退对齐位置</returns>
        private Point CalculateFallbackAlignedPosition(object sourceElement, Size targetSize)
        {
            var sourcePosition = GetElementPosition(sourceElement);
            var sourceSize = GetElementSize(sourceElement);
            
            double centerX = sourcePosition.X + sourceSize.Width / 2;
            double alignedX = centerX - targetSize.Width / 2;
            double alignedY = sourcePosition.Y + sourceSize.Height + SiemensGraphLayoutRules.STANDARD_BRANCH_SPACING;
            
            return new Point(alignedX, alignedY);
        }

        /// <summary>
        /// 获取元素位置
        /// </summary>
        /// <param name="element">元素</param>
        /// <returns>元素位置</returns>
        private Point GetElementPosition(object element)
        {
            return element switch
            {
                SFCBranchViewModel branchVM => branchVM.Position,
                SFCStepViewModel stepVM => stepVM.Position,
                SFCTransitionViewModel transitionVM => transitionVM.Position,
                _ => new Point(0, 0)
            };
        }

        /// <summary>
        /// 获取元素尺寸
        /// </summary>
        /// <param name="element">元素</param>
        /// <returns>元素尺寸</returns>
        private Size GetElementSize(object element)
        {
            return element switch
            {
                SFCBranchViewModel branchVM => new Size(branchVM.DynamicWidth, branchVM.DynamicHeight),
                SFCStepViewModel stepVM => stepVM.Size,
                SFCTransitionViewModel transitionVM => transitionVM.Size,
                _ => new Size(100, 50)
            };
        }

        /// <summary>
        /// 获取元素ID
        /// </summary>
        /// <param name="element">元素</param>
        /// <returns>元素ID</returns>
        private string GetElementId(object element)
        {
            return element switch
            {
                SFCBranchViewModel branchVM => branchVM.Id,
                SFCStepViewModel stepVM => stepVM.Id,
                SFCTransitionViewModel transitionVM => transitionVM.Id,
                _ => Guid.NewGuid().ToString()
            };
        }

        #endregion
    }
}
