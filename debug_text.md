[InsertSelectionBranchAfterStep] 选中元素: 初始步
[选择分支] 首次插入，现有分支: 0个
[GetActualConnectPointPosition] 步骤连接点: 索引0, 位置250,322
[CalculateConnectPointAlignedPositionForBranch] Selection分支精确对齐:
  源连接点: 250,322
  目标连接点偏移: 21,7
  计算位置: 229.5,319.5
[选择分支] 使用连接点精确对齐计算位置: 229.5,319.5
[选择分支] 新分支位置: 229.5,319.5
[选择分支] 创建: Initial, 位置: 229.5,319.5
[分支集合] 添加分支，位置: 229.5,319.5
[SFCBranchViewModel] 初始化适配器: BranchType=Selection, ElementType=SelectionBranch
[SFCBranchViewModel] ✅ 选择分支适配器配置完成，数量: 4
[SFCBranchViewModel] 🔧 索引1适配器类型: Input (修复为Input)
[SFCBranchViewModel] 🔧 索引2适配器类型: Output (修复为Output)
[SFCBranchViewModel] 🔧 临时修复：为选择分支设置默认转换条件编号 T1
[SFCBranchViewModel] 动态布局属性初始化:  -> 147x80
[SFCBranchViewModel] 🔍 关键属性值调试:
  - LeftLineX1: 20, LeftLineX2: 20
  - LeftLineLeft: 1
[MainHorizontalLineX2] 分支宽度147px → 横线长度147px → 末端坐标44.0px
  - MainHorizontalLineX1: -103, MainHorizontalLineX2: 44
  - MainHorizontalLineLeft: 124
  - ViewType: Initial, IsSelected: False
  - InternalTransitionNumber: 1, InternalTransitionHasLabel: True
[SFCBranchViewModel] ⚠️ 无法获取EnhancedSFCViewModel实例
[分支集合] ViewModel位置: 229.5,319.5
[SFCCanvas] 为新创建的分支添加位置变化监听: be0a6730-3428-4c35-8380-4f4fec7b6b8e
[分支闭合管理器] 注册分支: be0a6730-3428-4c35-8380-4f4fec7b6b8e, 初始路数: 1
[嵌套检测] 开始查找分支 be0a6730-3428-4c35-8380-4f4fec7b6b8e 的父分支
[嵌套检测] 找到 0 个输入连接
[空间检测] 开始基于空间关系查找父分支（就近原则）
[空间检测] 找到 0 个现有分支
[空间检测] 未找到空间关系的父分支
[嵌套检测] 未找到父分支
[EnhancedSFCViewModel] 未找到父分支，be0a6730-3428-4c35-8380-4f4fec7b6b8e 为顶层分支
[AddConnection] 开始执行: 2a44cce8-1d9e-4813-9ff5-49aa30df9646 -> be0a6730-3428-4c35-8380-4f4fec7b6b8e
[AddConnection] 对象查找结果: sourceObject=SFCStepModel, targetObject=SFCBranchModel
[AddConnection] 位置获取: 源ViewModel位置=200,200, 源Model位置=200,200
[AddConnection] 位置获取: 目标ViewModel位置=229.5,319.5, 目标Model位置=229.5,319.5
[GetPreciseConnectPointPosition] ⚠️ 回退到计算方法
[GetPreciseConnectPointPosition] ⚠️ 回退到计算方法
[CalculateElementConnectPoint] SFCBranchModel: 类型=Selection, 位置=229.5,319.5, 输出点=False
[对比调试] 如果是步骤输入点，位置应该是: 279.5,325.5
[CalculateElementConnectPoint] ✅ 选择分支左上连接点(索引0): 251,331
[AddConnection] 创建连接: 2a44cce8-1d9e-4813-9ff5-49aa30df9646 -> be0a6730-3428-4c35-8380-4f4fec7b6b8e
[AddConnection] 源位置: 200,200, 目标位置: 229.5,319.5
[AddConnection] 源对象类型: SFCStepModel, 目标对象类型: SFCBranchModel
[AddConnection] 源连接点: 250,322, 目标连接点: 251,331
[AddConnection] 连接创建完成，路径点数量: 2
[AddConnectionViewModel] 连接 d31e3a1f-bbde-4fe8-bdd8-000227f1e2f5 路径点数量: 2
[AddConnectionViewModel] ViewModel路径点数量: 2
[AddConnectionViewModel] 起点: 250,322, 终点: 251,331
[AddConnection] 🔄 开始更新连接点状态: 2a44cce8-1d9e-4813-9ff5-49aa30df9646 -> be0a6730-3428-4c35-8380-4f4fec7b6b8e
[UpdateElementConnectPointState] 🔍 开始更新连接点状态:
  元素类型: SFCStepViewModel
  连接点索引: 0
  是否输入: False
  连接ID: d31e3a1f-bbde-4fe8-bdd8-000227f1e2f5
[UpdateElementConnectPointState] ✅ 连接点状态更新成功: SFCStepViewModel, Direction=Output, Index=0, AdapterIndex=0, ConnectionId: d31e3a1f-bbde-4fe8-bdd8-000227f1e2f5
[UpdateElementConnectPointState] 🔍 开始更新连接点状态:
  元素类型: SFCBranchViewModel
  连接点索引: 0
  是否输入: True
  连接ID: d31e3a1f-bbde-4fe8-bdd8-000227f1e2f5
[UpdateElementConnectPointState] 🎯 找到分支元素适配器: be0a6730-3428-4c35-8380-4f4fec7b6b8e, 分支类型: Selection, 适配器数量: 4
[UpdateElementConnectPointState] ✅ 连接点状态更新成功: SFCBranchViewModel, Direction=Input, Index=0, AdapterIndex=0, ConnectionId: d31e3a1f-bbde-4fe8-bdd8-000227f1e2f5
[UpdateConnectPointStates] 连接点状态更新完成: 2a44cce8-1d9e-4813-9ff5-49aa30df9646[0] -> be0a6730-3428-4c35-8380-4f4fec7b6b8e[0]
[选择分支] 跳过垂直对齐，保持计算位置: 229.5,319.5
[选择分支] 左分支使用竖直连接线，无需创建转换条件
[选择分支] 右分支转换条件已内嵌在选择分支视图中，无需单独创建
System.Windows.Data Error: 40 : BindingExpression path error: 'Description' property not found on 'object' ''SFCBranchViewModel' (HashCode=26637782)'. BindingExpression:Path=SelectedElement.Description; DataItem='EnhancedSFCViewModel' (HashCode=34232944); target element is 'TextBlock' (Name=''); target property is 'Text' (type 'String')
[InitializeConnectPoints] 开始初始化连接点，ViewModel.Id: be0a6730-3428-4c35-8380-4f4fec7b6b8e
[InitializeConnectPoints] XAML连接点查找结果: LeftTop=True, LeftBottom=True, RightTop=True, RightBottom=True
[SFCSelectionBranchView] 开始设置连接点ElementId: be0a6730-3428-4c35-8380-4f4fec7b6b8e
[SFCSelectionBranchView] ✅ LeftTop连接点ElementId设置: be0a6730-3428-4c35-8380-4f4fec7b6b8e
[SFCSelectionBranchView] ✅ LeftBottom连接点ElementId设置: be0a6730-3428-4c35-8380-4f4fec7b6b8e
[SFCSelectionBranchView] ✅ RightTop连接点ElementId设置: be0a6730-3428-4c35-8380-4f4fec7b6b8e
[SFCSelectionBranchView] ✅ RightBottom连接点ElementId设置: be0a6730-3428-4c35-8380-4f4fec7b6b8e
[InitializeConnectPoints] LeftTop连接点ElementId: 'be0a6730-3428-4c35-8380-4f4fec7b6b8e', 期望: 'be0a6730-3428-4c35-8380-4f4fec7b6b8e'
[InitializeConnectPoints] ✅ 已修复LeftTop连接点ElementId: 'be0a6730-3428-4c35-8380-4f4fec7b6b8e'
[SFCSelectionBranchView] 适配器检查: Count=4, 期望>=4
[SFCSelectionBranchView] ✅ LeftTop适配器绑定: Direction=Input, Index=0
[SFCSelectionBranchView] ✅ LeftBottom适配器绑定: Direction=Input, Index=1
[SFCSelectionBranchView] ✅ RightTop适配器绑定: Direction=Output, Index=2
[SFCSelectionBranchView] ✅ RightBottom适配器绑定: Direction=Output, Index=3
[SFCSelectionBranchView] 开始同步现有连接状态: be0a6730-3428-4c35-8380-4f4fec7b6b8e
[SFCSelectionBranchView] 找到 1 个相关连接
[SFCSelectionBranchView] ✅ 同步输入连接: d31e3a1f-bbde-4fe8-bdd8-000227f1e2f5, 索引: 0
[MainHorizontalLineX2] 分支宽度147px → 横线长度147px → 末端坐标44.0px
[MainHorizontalLineX2] 分支宽度147px → 横线长度147px → 末端坐标44.0px
[RightConnectionLineLeft] 主横线终点=168.0, 右侧竖线位置=165.5
[MainHorizontalLineX2] 分支宽度147px → 横线长度147px → 末端坐标44.0px
[TransitionConnectionLineLeft] 主横线终点=168.0, 连接虚线位置=107.0
[MainHorizontalLineX2] 分支宽度147px → 横线长度147px → 末端坐标44.0px
[TransitionCrossLineLeft] 主横线终点=168.0, 十字横线位置=107.0
[MainHorizontalLineX2] 分支宽度147px → 横线长度147px → 末端坐标44.0px
[TransitionContactLeftLineLeft] 主横线终点=168.0, 触点左竖线位置=128.0
[MainHorizontalLineX2] 分支宽度147px → 横线长度147px → 末端坐标44.0px
[TransitionContactRightLineLeft] 主横线终点=168.0, 触点右竖线位置=135.0
[MainHorizontalLineX2] 分支宽度147px → 横线长度147px → 末端坐标44.0px
[RightTopConnectPointLeft] 主横线终点=168.0, 右上连接点位置=162.0
[MainHorizontalLineX2] 分支宽度147px → 横线长度147px → 末端坐标44.0px
[RightBottomConnectPointLeft] 主横线终点=168.0, 右下连接点位置=162.0
[MainHorizontalLineX2] 分支宽度147px → 横线长度147px → 末端坐标44.0px
[TransitionIdentifierLeft] 主横线终点=168.0, T1位置=176.0
[InitializeConnectPoints] 连接点已初始化，跳过重复初始化: be0a6730-3428-4c35-8380-4f4fec7b6b8e
System.Windows.Data Error: 4 : Cannot find source for binding with reference 'RelativeSource FindAncestor, AncestorType='System.Windows.Controls.UserControl', AncestorLevel='1''. BindingExpression:Path=DataContext.InsertSelectionBranchFromAnyPartCommand; DataItem=null; target element is 'MenuItem' (Name=''); target property is 'Command' (type 'ICommand')
System.Windows.Data Error: 4 : Cannot find source for binding with reference 'RelativeSource FindAncestor, AncestorType='System.Windows.Controls.UserControl', AncestorLevel='1''. BindingExpression:Path=DataContext.InsertSiblingBranchCommand; DataItem=null; target element is 'MenuItem' (Name=''); target property is 'Command' (type 'ICommand')
System.Windows.Data Error: 4 : Cannot find source for binding with reference 'RelativeSource FindAncestor, AncestorType='System.Windows.Controls.UserControl', AncestorLevel='1''. BindingExpression:Path=DataContext.InsertParallelBranchFromAnyPartCommand; DataItem=null; target element is 'MenuItem' (Name=''); target property is 'Command' (type 'ICommand')
[连接点重叠检测] 点1: (250.0, 322.0), 点2: (251.0, 331.0), 距离: 9.1px, 重叠: True
[CreateConnectionPath] 连接 d31e3a1f-bbde-4fe8-bdd8-000227f1e2f5 连接点重叠，隐藏连接线
[CreateConnectionPath] 起点: (250.0, 322.0), 终点: (251.0, 331.0)
连接 d31e3a1f-bbde-4fe8-bdd8-000227f1e2f5 连接点重叠，使用连接点重叠方式
[AddConnection] 延迟更新连接线 d31e3a1f-bbde-4fe8-bdd8-000227f1e2f5 的路径点
[CalculateElementConnectPoint] ✅ 选择分支ViewModel左上连接点(索引0): 250,326.5
[AddConnection] 延迟更新 - 源对象类型: SFCStepViewModel, 目标对象类型: SFCBranchViewModel
[AddConnection] 延迟更新 - 源位置: 200,200, 目标位置: 229.5,319.5
[AddConnection] 延迟更新后的路径点: 250,322 -> 250,326.5
[AddConnection] PathPoints集合已更新，应该触发重新评估
“PC_Control2.Demo.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.NETCore.App\8.0.17\System.Runtime.Intrinsics.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
[SFCCanvas] 位置变化: SFCBranchViewModel.IsSelected
[SFCCanvas] 位置变化: SFCBranchViewModel.SelectedPart
[SFCSelectionBranchView] 拖动更新位置: be0a6730-3428-4c35-8380-4f4fec7b6b8e -> 229.5,319.5
[SFCSelectionBranchView] 右侧区域选中并开始拖拽: be0a6730-3428-4c35-8380-4f4fec7b6b8e
[SFCBranchView] 接收到鼠标点击事件: Id=be0a6730-3428-4c35-8380-4f4fec7b6b8e, BranchType=Selection
[SFCBranchView] 选择分支，只处理选中逻辑
[SFCCanvas] 位置变化: SFCBranchViewModel.IsSelected
System.Windows.Data Error: 40 : BindingExpression path error: 'Description' property not found on 'object' ''SFCBranchViewModel' (HashCode=26637782)'. BindingExpression:Path=SelectedElement.Description; DataItem='EnhancedSFCViewModel' (HashCode=34232944); target element is 'TextBlock' (Name=''); target property is 'Text' (type 'String')
“PC_Control2.Demo.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.WindowsDesktop.App\8.0.17\Accessibility.dll”。模块已生成，不包含符号。
[InsertSiblingBranch] 横线端点连接位置计算:
  当前分支位置: X=229.5, Y=319.5
  当前分支ViewType: Initial
  前一个分支横线右端: 369.5
  连接间隙: 0
  计算公式: 369.5 + 0 - 40 - (-47)
  新分支位置: X=376.5, Y=319.5
  验证：新分支横线最左端 = 369.5
  常量值: RIGHT_PART_LEFT=40, LINE_LEFT_OFFSET=-47, LINE_RIGHT_OFFSET=100, GAP=0
[SFCBranchViewModel] 初始化适配器: BranchType=Selection, ElementType=SelectionBranch
[SFCBranchViewModel] ✅ 选择分支适配器配置完成，数量: 4
[SFCBranchViewModel] 🔧 索引1适配器类型: Input (修复为Input)
[SFCBranchViewModel] 🔧 索引2适配器类型: Output (修复为Output)
[SFCBranchViewModel] 🔧 临时修复：为选择分支设置默认转换条件编号 T1
[SFCBranchViewModel] 动态布局属性初始化:  -> 147x80
[SFCBranchViewModel] 🔍 关键属性值调试:
  - LeftLineX1: 20, LeftLineX2: 20
  - LeftLineLeft: 1
[MainHorizontalLineX2] 分支宽度147px → 横线长度147px → 末端坐标44.0px
  - MainHorizontalLineX1: -103, MainHorizontalLineX2: 44
  - MainHorizontalLineLeft: 124
  - ViewType: Initial, IsSelected: False
  - InternalTransitionNumber: 1, InternalTransitionHasLabel: True
[SFCBranchViewModel] ⚠️ 无法获取EnhancedSFCViewModel实例
[SFCCanvas] 位置变化: SFCBranchViewModel.NextBranchId
[SFCCanvas] 为新创建的分支添加位置变化监听: 97a4c5dd-2d07-4d05-9d75-2bfec6c06ebe
[CreateBranchChainConnection] 选择分支扩展：使用左侧下端连接点（索引1）
[CreateBranchChainConnection] 分支链连接: Selection 源索引2 -> Selection 目标索引1
[CreateBranchChainConnection] 创建分支链连接: be0a6730-3428-4c35-8380-4f4fec7b6b8e -> 97a4c5dd-2d07-4d05-9d75-2bfec6c06ebe
[AddConnection] 开始执行: be0a6730-3428-4c35-8380-4f4fec7b6b8e -> 97a4c5dd-2d07-4d05-9d75-2bfec6c06ebe
[AddConnection] 对象查找结果: sourceObject=SFCBranchModel, targetObject=SFCBranchModel
[AddConnection] 位置获取: 源ViewModel位置=229.5,319.5, 源Model位置=229.5,319.5
[AddConnection] 位置获取: 目标ViewModel位置=376.5,319.5, 目标Model位置=376.5,319.5
[GetPreciseConnectPointPosition] ⚠️ 回退到计算方法
[CalculateElementConnectPoint] SFCBranchModel: 类型=Selection, 位置=229.5,319.5, 输出点=True
[CalculateElementConnectPoint] ✅ 选择分支右上连接点(索引2): 396.5,354.5
[GetPreciseConnectPointPosition] ⚠️ 回退到计算方法
[CalculateElementConnectPoint] SFCBranchModel: 类型=Selection, 位置=376.5,319.5, 输出点=False
[对比调试] 如果是步骤输入点，位置应该是: 426.5,325.5
[CalculateElementConnectPoint] ✅ 选择分支左下连接点(索引1): 405.5,354.5
[AddConnection] 创建连接: be0a6730-3428-4c35-8380-4f4fec7b6b8e -> 97a4c5dd-2d07-4d05-9d75-2bfec6c06ebe
[AddConnection] 源位置: 229.5,319.5, 目标位置: 376.5,319.5
[AddConnection] 源对象类型: SFCBranchModel, 目标对象类型: SFCBranchModel
[AddConnection] 源连接点: 396.5,354.5, 目标连接点: 405.5,354.5
[AddConnection] 连接创建完成，路径点数量: 2
[AddConnectionViewModel] 连接 57d45744-1a95-4387-b644-484dafbb9ac6 路径点数量: 2
[AddConnectionViewModel] ViewModel路径点数量: 2
[AddConnectionViewModel] 起点: 396.5,354.5, 终点: 405.5,354.5
[AddConnection] 🔄 开始更新连接点状态: be0a6730-3428-4c35-8380-4f4fec7b6b8e -> 97a4c5dd-2d07-4d05-9d75-2bfec6c06ebe
[UpdateElementConnectPointState] 🔍 开始更新连接点状态:
  元素类型: SFCBranchViewModel
  连接点索引: 2
  是否输入: False
  连接ID: 57d45744-1a95-4387-b644-484dafbb9ac6
[UpdateElementConnectPointState] 🎯 找到分支元素适配器: be0a6730-3428-4c35-8380-4f4fec7b6b8e, 分支类型: Selection, 适配器数量: 4
[UpdateElementConnectPointState] ✅ 连接点状态更新成功: SFCBranchViewModel, Direction=Output, Index=2, AdapterIndex=2, ConnectionId: 57d45744-1a95-4387-b644-484dafbb9ac6
[UpdateElementConnectPointState] 🔍 开始更新连接点状态:
  元素类型: SFCBranchViewModel
  连接点索引: 1
  是否输入: True
  连接ID: 57d45744-1a95-4387-b644-484dafbb9ac6
[UpdateElementConnectPointState] 🎯 找到分支元素适配器: 97a4c5dd-2d07-4d05-9d75-2bfec6c06ebe, 分支类型: Selection, 适配器数量: 4
[UpdateElementConnectPointState] ✅ 连接点状态更新成功: SFCBranchViewModel, Direction=Input, Index=1, AdapterIndex=1, ConnectionId: 57d45744-1a95-4387-b644-484dafbb9ac6
[UpdateConnectPointStates] 连接点状态更新完成: be0a6730-3428-4c35-8380-4f4fec7b6b8e[2] -> 97a4c5dd-2d07-4d05-9d75-2bfec6c06ebe[1]
[ValidateAndAdjustBranchChainLayout] 调整分支链布局，共2个分支
[ValidateAndAdjustBranchChainLayout] 分支链布局调整完成
[嵌套检测] 开始查找分支 97a4c5dd-2d07-4d05-9d75-2bfec6c06ebe 的父分支
[嵌套检测] 找到 1 个输入连接
[嵌套检测] 源元素类型: SFCBranchModel, ID: be0a6730-3428-4c35-8380-4f4fec7b6b8e
[嵌套检测] 通过追溯找到父分支: be0a6730-3428-4c35-8380-4f4fec7b6b8e
[EnhancedSFCViewModel] 获取所有元素: 步骤=1, 转换=0, 分支=2, 连接=2, 动作=0
[路数计算] 选择分支 be0a6730-3428-4c35-8380-4f4fec7b6b8e: 出口连接数=1, 路数=1
[分支闭合管理器] 重新计算实际路数: be0a6730-3428-4c35-8380-4f4fec7b6b8e = 1路
[分支闭合管理器] 宽度计算详情: be0a6730-3428-4c35-8380-4f4fec7b6b8e
  - 旧路数: 1, 新路数: 2
  - 旧宽度: 147px, 新宽度: 294px
  - 嵌套分支数量: 1
[分支闭合管理器] 内部注册分支: 97a4c5dd-2d07-4d05-9d75-2bfec6c06ebe, 初始路数: 1
[简化宽度计算器] 查找受影响的父分支: be0a6730-3428-4c35-8380-4f4fec7b6b8e
[简化宽度计算器] 缓存清理完成: be0a6730-3428-4c35-8380-4f4fec7b6b8e, 受影响父分支数: 0
[SFCCanvas] 位置变化: SFCBranchViewModel.DynamicWidth
[SFCCanvas] 位置变化: SFCBranchViewModel.MainHorizontalLineX2
[MainHorizontalLineX2] 分支宽度294px → 横线长度294px → 末端坐标191.0px
[SFCCanvas] 位置变化: SFCBranchViewModel.RightTopConnectPointLeft
[MainHorizontalLineX2] 分支宽度294px → 横线长度294px → 末端坐标191.0px
[RightTopConnectPointLeft] 主横线终点=315.0, 右上连接点位置=309.0
[SFCCanvas] 位置变化: SFCBranchViewModel.RightBottomConnectPointLeft
[MainHorizontalLineX2] 分支宽度294px → 横线长度294px → 末端坐标191.0px
[RightBottomConnectPointLeft] 主横线终点=315.0, 右下连接点位置=309.0
[SFCCanvas] 位置变化: SFCBranchViewModel.RightConnectionLineLeft
[MainHorizontalLineX2] 分支宽度294px → 横线长度294px → 末端坐标191.0px
[RightConnectionLineLeft] 主横线终点=315.0, 右侧竖线位置=312.5
[SFCCanvas] 位置变化: SFCBranchViewModel.TransitionLineLeft
[SFCCanvas] 位置变化: SFCBranchViewModel.TransitionIdentifierLeft
[MainHorizontalLineX2] 分支宽度294px → 横线长度294px → 末端坐标191.0px
[TransitionIdentifierLeft] 主横线终点=315.0, T1位置=323.0
[SFCCanvas] 位置变化: SFCBranchViewModel.TransitionConnectionLineLeft
[MainHorizontalLineX2] 分支宽度294px → 横线长度294px → 末端坐标191.0px
[TransitionConnectionLineLeft] 主横线终点=315.0, 连接虚线位置=254.0
[SFCCanvas] 位置变化: SFCBranchViewModel.TransitionCrossLineLeft
[MainHorizontalLineX2] 分支宽度294px → 横线长度294px → 末端坐标191.0px
[TransitionCrossLineLeft] 主横线终点=315.0, 十字横线位置=254.0
[SFCCanvas] 位置变化: SFCBranchViewModel.TransitionContactLeftLineLeft
[MainHorizontalLineX2] 分支宽度294px → 横线长度294px → 末端坐标191.0px
[TransitionContactLeftLineLeft] 主横线终点=315.0, 触点左竖线位置=275.0
[SFCCanvas] 位置变化: SFCBranchViewModel.TransitionContactRightLineLeft
[MainHorizontalLineX2] 分支宽度294px → 横线长度294px → 末端坐标191.0px
[TransitionContactRightLineLeft] 主横线终点=315.0, 触点右竖线位置=282.0
[SFCCanvas] 位置变化: SFCBranchViewModel.RightInteractionLayerWidth
[SFCCanvas] 位置变化: SFCBranchViewModel.ParallelBranchWidth
[SFCCanvas] 位置变化: SFCBranchViewModel.ParallelRightVerticalLeft
[SFCCanvas] 位置变化: SFCBranchViewModel.ParallelRightConnectPointLeft
[SFCCanvas] 位置变化: SFCBranchViewModel.ParallelInteractionLayerWidth
[SFCCanvas] 位置变化: SFCBranchViewModel.Size
[EnhancedSFCViewModel] 分支宽度已调整: be0a6730-3428-4c35-8380-4f4fec7b6b8e 147px -> 294px
[简化宽度计算器] 宽度调整: be0a6730-3428-4c35-8380-4f4fec7b6b8e 147px -> 294px
[简化宽度计算器] 嵌套分支插入，清除缓存: be0a6730-3428-4c35-8380-4f4fec7b6b8e, 97a4c5dd-2d07-4d05-9d75-2bfec6c06ebe
[EnhancedSFCViewModel] 嵌套分支插入: be0a6730-3428-4c35-8380-4f4fec7b6b8e -> 97a4c5dd-2d07-4d05-9d75-2bfec6c06ebe
[分支闭合管理器] 嵌套分支插入成功: be0a6730-3428-4c35-8380-4f4fec7b6b8e -> 97a4c5dd-2d07-4d05-9d75-2bfec6c06ebe, 新宽度: 294px
[EnhancedSFCViewModel] 成功检测并处理嵌套分支插入: be0a6730-3428-4c35-8380-4f4fec7b6b8e -> 97a4c5dd-2d07-4d05-9d75-2bfec6c06ebe
System.Windows.Data Error: 40 : BindingExpression path error: 'Description' property not found on 'object' ''SFCBranchViewModel' (HashCode=2168452)'. BindingExpression:Path=SelectedElement.Description; DataItem='EnhancedSFCViewModel' (HashCode=34232944); target element is 'TextBlock' (Name=''); target property is 'Text' (type 'String')
[EnhancedSFCViewModel] 需要刷新连接线: d31e3a1f-bbde-4fe8-bdd8-000227f1e2f5
[EnhancedSFCViewModel] 需要刷新连接线: 57d45744-1a95-4387-b644-484dafbb9ac6
[EnhancedSFCViewModel] 已刷新分支 be0a6730-3428-4c35-8380-4f4fec7b6b8e 相关的 2 条连接线
[InitializeConnectPoints] 开始初始化连接点，ViewModel.Id: 97a4c5dd-2d07-4d05-9d75-2bfec6c06ebe
[InitializeConnectPoints] XAML连接点查找结果: LeftTop=True, LeftBottom=True, RightTop=True, RightBottom=True
[SFCSelectionBranchView] 开始设置连接点ElementId: 97a4c5dd-2d07-4d05-9d75-2bfec6c06ebe
[SFCSelectionBranchView] ✅ LeftTop连接点ElementId设置: 97a4c5dd-2d07-4d05-9d75-2bfec6c06ebe
[SFCSelectionBranchView] ✅ LeftBottom连接点ElementId设置: 97a4c5dd-2d07-4d05-9d75-2bfec6c06ebe
[SFCSelectionBranchView] ✅ RightTop连接点ElementId设置: 97a4c5dd-2d07-4d05-9d75-2bfec6c06ebe
[SFCSelectionBranchView] ✅ RightBottom连接点ElementId设置: 97a4c5dd-2d07-4d05-9d75-2bfec6c06ebe
[InitializeConnectPoints] LeftTop连接点ElementId: '97a4c5dd-2d07-4d05-9d75-2bfec6c06ebe', 期望: '97a4c5dd-2d07-4d05-9d75-2bfec6c06ebe'
[InitializeConnectPoints] ✅ 已修复LeftTop连接点ElementId: '97a4c5dd-2d07-4d05-9d75-2bfec6c06ebe'
[SFCSelectionBranchView] 适配器检查: Count=4, 期望>=4
[SFCSelectionBranchView] ✅ LeftTop适配器绑定: Direction=Input, Index=0
[SFCSelectionBranchView] ✅ LeftBottom适配器绑定: Direction=Input, Index=1
[SFCSelectionBranchView] ✅ RightTop适配器绑定: Direction=Output, Index=2
[SFCSelectionBranchView] ✅ RightBottom适配器绑定: Direction=Output, Index=3
[SFCSelectionBranchView] 开始同步现有连接状态: 97a4c5dd-2d07-4d05-9d75-2bfec6c06ebe
[SFCSelectionBranchView] 找到 1 个相关连接
[SFCSelectionBranchView] ✅ 同步输入连接: 57d45744-1a95-4387-b644-484dafbb9ac6, 索引: 1
[MainHorizontalLineX2] 分支宽度147px → 横线长度147px → 末端坐标44.0px
[MainHorizontalLineX2] 分支宽度147px → 横线长度147px → 末端坐标44.0px
[RightConnectionLineLeft] 主横线终点=168.0, 右侧竖线位置=165.5
[MainHorizontalLineX2] 分支宽度147px → 横线长度147px → 末端坐标44.0px
[TransitionConnectionLineLeft] 主横线终点=168.0, 连接虚线位置=107.0
[MainHorizontalLineX2] 分支宽度147px → 横线长度147px → 末端坐标44.0px
[TransitionCrossLineLeft] 主横线终点=168.0, 十字横线位置=107.0
[MainHorizontalLineX2] 分支宽度147px → 横线长度147px → 末端坐标44.0px
[TransitionContactLeftLineLeft] 主横线终点=168.0, 触点左竖线位置=128.0
[MainHorizontalLineX2] 分支宽度147px → 横线长度147px → 末端坐标44.0px
[TransitionContactRightLineLeft] 主横线终点=168.0, 触点右竖线位置=135.0
[MainHorizontalLineX2] 分支宽度147px → 横线长度147px → 末端坐标44.0px
[RightTopConnectPointLeft] 主横线终点=168.0, 右上连接点位置=162.0
[MainHorizontalLineX2] 分支宽度147px → 横线长度147px → 末端坐标44.0px
[RightBottomConnectPointLeft] 主横线终点=168.0, 右下连接点位置=162.0
[MainHorizontalLineX2] 分支宽度147px → 横线长度147px → 末端坐标44.0px
[TransitionIdentifierLeft] 主横线终点=168.0, T1位置=176.0
[InitializeConnectPoints] 连接点已初始化，跳过重复初始化: 97a4c5dd-2d07-4d05-9d75-2bfec6c06ebe
System.Windows.Data Error: 4 : Cannot find source for binding with reference 'RelativeSource FindAncestor, AncestorType='System.Windows.Controls.UserControl', AncestorLevel='1''. BindingExpression:Path=DataContext.InsertSelectionBranchFromAnyPartCommand; DataItem=null; target element is 'MenuItem' (Name=''); target property is 'Command' (type 'ICommand')
System.Windows.Data Error: 4 : Cannot find source for binding with reference 'RelativeSource FindAncestor, AncestorType='System.Windows.Controls.UserControl', AncestorLevel='1''. BindingExpression:Path=DataContext.InsertSiblingBranchCommand; DataItem=null; target element is 'MenuItem' (Name=''); target property is 'Command' (type 'ICommand')
System.Windows.Data Error: 4 : Cannot find source for binding with reference 'RelativeSource FindAncestor, AncestorType='System.Windows.Controls.UserControl', AncestorLevel='1''. BindingExpression:Path=DataContext.InsertParallelBranchFromAnyPartCommand; DataItem=null; target element is 'MenuItem' (Name=''); target property is 'Command' (type 'ICommand')
[连接点重叠检测] 点1: (396.5, 354.5), 点2: (405.5, 354.5), 距离: 9.0px, 重叠: True
[CreateConnectionPath] 连接 57d45744-1a95-4387-b644-484dafbb9ac6 连接点重叠，隐藏连接线
[CreateConnectionPath] 起点: (396.5, 354.5), 终点: (405.5, 354.5)
连接 57d45744-1a95-4387-b644-484dafbb9ac6 连接点重叠，使用连接点重叠方式
[AddConnection] 延迟更新连接线 57d45744-1a95-4387-b644-484dafbb9ac6 的路径点
[CalculateElementConnectPoint] ✅ 选择分支ViewModel右上连接点(索引2): 396,354.5
[CalculateElementConnectPoint] ✅ 选择分支ViewModel左下连接点(索引1): 397,354.5
[AddConnection] 延迟更新 - 源对象类型: SFCBranchViewModel, 目标对象类型: SFCBranchViewModel
[AddConnection] 延迟更新 - 源位置: 229.5,319.5, 目标位置: 376.5,319.5
[AddConnection] 延迟更新后的路径点: 396,354.5 -> 397,354.5
[AddConnection] PathPoints集合已更新，应该触发重新评估
[嵌套检测] 开始查找分支 97a4c5dd-2d07-4d05-9d75-2bfec6c06ebe 的父分支
[嵌套检测] 找到 1 个输入连接
[嵌套检测] 源元素类型: SFCBranchModel, ID: be0a6730-3428-4c35-8380-4f4fec7b6b8e
[嵌套检测] 通过追溯找到父分支: be0a6730-3428-4c35-8380-4f4fec7b6b8e
[分支闭合管理器] 分支 97a4c5dd-2d07-4d05-9d75-2bfec6c06ebe 已经是 be0a6730-3428-4c35-8380-4f4fec7b6b8e 的嵌套分支，跳过重复插入
[SFCCanvas] 位置变化: SFCBranchViewModel.IsSelected
