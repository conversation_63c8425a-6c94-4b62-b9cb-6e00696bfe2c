﻿#pragma checksum "..\..\..\..\Controls\SFCParallelBranchView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "D5D3B6D22F28BDE25405E42BAA70872A89B7ED8A"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using PC_Control2.Demo.Controls;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace PC_Control2.Demo.Controls {
    
    
    /// <summary>
    /// SFCParallelBranchView
    /// </summary>
    public partial class SFCParallelBranchView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 66 "..\..\..\..\Controls\SFCParallelBranchView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Canvas MainCanvas;
        
        #line default
        #line hidden
        
        
        #line 72 "..\..\..\..\Controls\SFCParallelBranchView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Rectangle ParallelBranchInteractionLayer;
        
        #line default
        #line hidden
        
        
        #line 103 "..\..\..\..\Controls\SFCParallelBranchView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Line LeftVerticalLine;
        
        #line default
        #line hidden
        
        
        #line 201 "..\..\..\..\Controls\SFCParallelBranchView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal PC_Control2.Demo.Controls.SFCConnectPoint LeftTopConnectPoint;
        
        #line default
        #line hidden
        
        
        #line 224 "..\..\..\..\Controls\SFCParallelBranchView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal PC_Control2.Demo.Controls.SFCConnectPoint LeftParallelPoint;
        
        #line default
        #line hidden
        
        
        #line 243 "..\..\..\..\Controls\SFCParallelBranchView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal PC_Control2.Demo.Controls.SFCConnectPoint RightParallelPoint;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/PC_Control2.Demo;component/controls/sfcparallelbranchview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Controls\SFCParallelBranchView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal System.Delegate _CreateDelegate(System.Type delegateType, string handler) {
            return System.Delegate.CreateDelegate(delegateType, this, handler);
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.MainCanvas = ((System.Windows.Controls.Canvas)(target));
            return;
            case 2:
            this.ParallelBranchInteractionLayer = ((System.Windows.Shapes.Rectangle)(target));
            return;
            case 3:
            this.LeftVerticalLine = ((System.Windows.Shapes.Line)(target));
            return;
            case 4:
            this.LeftTopConnectPoint = ((PC_Control2.Demo.Controls.SFCConnectPoint)(target));
            return;
            case 5:
            this.LeftParallelPoint = ((PC_Control2.Demo.Controls.SFCConnectPoint)(target));
            return;
            case 6:
            this.RightParallelPoint = ((PC_Control2.Demo.Controls.SFCConnectPoint)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

