using System.Collections.Generic;
using System.Windows;
using System.Windows.Input;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using PC_Control2.Demo.Models;

namespace PC_Control2.Demo.ViewModels
{
    /// <summary>
    /// SFC分支的ViewModel - 完整实现
    /// </summary>
    public class SFCBranchViewModel : ObservableObject
    {
        private string _id = string.Empty;
        private string _name = string.Empty;
        private SFCBranchType _branchType = SFCBranchType.Selection;
        private Point _position = new Point(0, 0);
        private Size _size = new Size(120, 12);
        private bool _isSelected = false;
        private bool _isConvergence = false;
        private bool _isInitialStep = false; // 是否为初始步骤
        private string _selectedPart = "None"; // 选择分支的选中部分：Left, Right, None
        private BranchViewType _viewType = BranchViewType.Initial; // 分支显示类型
        private string? _nextBranchId = null; // 下一个兄弟分支的ID
        private int _internalTransitionNumber = 0; // 内部转换条件编号
        private string _internalTransitionCondition = "TRUE"; // 内部转换条件表达式

        // 动态布局属性
        private double _dynamicWidth = 147.0; // 动态宽度，默认值基于现有XAML
        private double _dynamicHeight = 80.0; // 动态高度，默认值基于现有XAML
        private bool _requiresDynamicAdjustment = false; // 是否需要动态调整

        public string Id
        {
            get => _id;
            set => SetProperty(ref _id, value);
        }

        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        public SFCBranchType BranchType
        {
            get => _branchType;
            set
            {
                if (SetProperty(ref _branchType, value))
                {
                    // 🔧 关键修复：当分支类型变化时，重新初始化适配器
                    // 这解决了构造函数时序问题：构造时BranchType还是默认值，属性设置时才是正确值
                    InitializeConnectPointAdapters();

                    // 🔧 新增：重新初始化动态布局属性
                    InitializeDynamicLayoutProperties();

                    System.Diagnostics.Debug.WriteLine($"[SFCBranchViewModel] 🔄 分支类型变更为{value}，重新初始化适配器和动态布局，适配器数量: {ConnectPointAdapters.Count}");
                }
            }
        }

        public Point Position
        {
            get => _position;
            set
            {
                if (SetProperty(ref _position, value))
                {
                    // 🔧 关键修复：同步位置到对应的Model
                    SyncPositionToModel(value);
                }
            }
        }

        public Size Size
        {
            get => new Size(DynamicWidth, DynamicHeight); // 使用动态尺寸
            set => SetProperty(ref _size, value);
        }

        public bool IsSelected
        {
            get => _isSelected;
            set => SetProperty(ref _isSelected, value);
        }

        public bool IsConvergence
        {
            get => _isConvergence;
            set => SetProperty(ref _isConvergence, value);
        }

        /// <summary>
        /// 选择分支的选中部分：Left表示左侧竖线部分，Right表示右侧转换条件部分，None表示未选中
        /// </summary>
        public string SelectedPart
        {
            get => _selectedPart;
            set => SetProperty(ref _selectedPart, value);
        }

        /// <summary>
        /// 分支显示类型：Initial表示首次分支样式，Subsequent表示后续分支样式
        /// </summary>
        public BranchViewType ViewType
        {
            get => _viewType;
            set => SetProperty(ref _viewType, value);
        }

        /// <summary>
        /// 下一个兄弟分支的ID，用于构建分支链
        /// </summary>
        public string? NextBranchId
        {
            get => _nextBranchId;
            set => SetProperty(ref _nextBranchId, value);
        }

        public int InternalTransitionNumber
        {
            get => _internalTransitionNumber;
            set
            {
                if (SetProperty(ref _internalTransitionNumber, value))
                {
                    OnPropertyChanged(nameof(InternalTransitionDisplayText));
                    OnPropertyChanged(nameof(InternalTransitionHasLabel));
                    
                    // 🔧 关键修复：同步更新对应的Model中的InternalTransition.TransitionNumber
                    SyncInternalTransitionNumberToModel(value);
                }
            }
        }
        /// <summary>
        /// 🔧 关键修复：同步InternalTransitionNumber到对应的Model
        /// </summary>
        /// <param name="transitionNumber">转换条件编号</param>
        private void SyncInternalTransitionNumberToModel(int transitionNumber)
        {
            try
            {
                var enhancedViewModel = GetEnhancedSFCViewModel?.Invoke();
                if (enhancedViewModel != null)
                {
                    // 通过ID查找对应的Model
                    var correspondingModel = enhancedViewModel.Branches.FirstOrDefault(b => b.Id == Id);
                    if (correspondingModel != null && correspondingModel.BranchType == SFCBranchType.Selection)
                    {
                        // 如果Model还没有InternalTransition，创建一个
                        if (correspondingModel.InternalTransition == null && transitionNumber > 0)
                        {
                            correspondingModel.InternalTransition = new SFCTransitionModel
                            {
                                Id = Guid.NewGuid().ToString(),
                                Name = $"转换{transitionNumber}",
                                TransitionNumber = transitionNumber,
                                Position = correspondingModel.Position,
                                Size = new Size(60, 8),
                                ConditionExpression = "TRUE"
                            };
                            System.Diagnostics.Debug.WriteLine($"[SyncInternalTransitionNumberToModel] 🔧 为Model创建InternalTransition: {Id} -> T{transitionNumber}");
                        }
                        // 如果Model已有InternalTransition，更新编号
                        else if (correspondingModel.InternalTransition != null)
                        {
                            correspondingModel.InternalTransition.TransitionNumber = transitionNumber;
                            System.Diagnostics.Debug.WriteLine($"[SyncInternalTransitionNumberToModel] 🔧 更新Model的TransitionNumber: {Id} -> T{transitionNumber}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[SyncInternalTransitionNumberToModel] ⚠️ 同步失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 🔧 从EnhancedSFCViewModel获取下一个可用的转换条件编号
        /// </summary>
        /// <param name="enhancedViewModel">EnhancedSFCViewModel实例</param>
        /// <returns>下一个可用的转换条件编号</returns>
        private int GetNextTransitionNumberFromEnhanced(ViewModels.EnhancedSFCViewModel enhancedViewModel)
        {
            try
            {
                // 收集所有现有的转换条件编号
                var existingNumbers = new HashSet<int>();

                // 收集独立转换条件的编号
                foreach (var transition in enhancedViewModel.Transitions)
                {
                    if (transition.TransitionNumber > 0)
                    {
                        existingNumbers.Add(transition.TransitionNumber);
                    }
                }

                // 收集选择分支内部转换条件的编号
                foreach (var branch in enhancedViewModel.Branches)
                {
                    if (branch.BranchType == SFCBranchType.Selection &&
                        branch.InternalTransition != null &&
                        branch.InternalTransition.TransitionNumber > 0)
                    {
                        existingNumbers.Add(branch.InternalTransition.TransitionNumber);
                    }
                }

                // 如果没有现有编号，从1开始
                if (existingNumbers.Count == 0)
                {
                    return 1;
                }

                // 将编号排序，查找第一个缺失的编号
                var sortedNumbers = existingNumbers.OrderBy(n => n).ToList();

                // 检查从1开始是否有缺失的编号
                for (int i = 1; i <= sortedNumbers.Max(); i++)
                {
                    if (!existingNumbers.Contains(i))
                    {
                        return i; // 返回第一个缺失的编号
                    }
                }

                // 如果没有缺失编号，返回最大编号+1
                return sortedNumbers.Max() + 1;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[GetNextTransitionNumberFromEnhanced] ⚠️ 获取编号失败: {ex.Message}");
                return 1; // 出错时返回默认值
            }
        }

        public string InternalTransitionCondition
        {
            get => _internalTransitionCondition;
            set => SetProperty(ref _internalTransitionCondition, value);
        }

        // 内部转换条件的显示文本（格式：T{编号}）
        public string InternalTransitionDisplayText => InternalTransitionNumber > 0 ? $"T{InternalTransitionNumber}" : string.Empty;

        // 内部转换条件是否有标签
        public bool InternalTransitionHasLabel => InternalTransitionNumber > 0;

        #region 动态布局属性

        /// <summary>
        /// 动态宽度 - 用于XAML绑定
        /// </summary>
        public double DynamicWidth
        {
            get => _dynamicWidth;
            set
            {
                if (SetProperty(ref _dynamicWidth, value))
                {
                    // 通知所有依赖DynamicWidth的计算属性
                    OnPropertyChanged(nameof(MainHorizontalLineX2));
                    OnPropertyChanged(nameof(RightTopConnectPointLeft));
                    OnPropertyChanged(nameof(RightBottomConnectPointLeft));
                    OnPropertyChanged(nameof(RightConnectionLineLeft));
                    OnPropertyChanged(nameof(TransitionLineLeft));
                    OnPropertyChanged(nameof(TransitionIdentifierLeft));
                    OnPropertyChanged(nameof(TransitionConnectionLineLeft));
                    OnPropertyChanged(nameof(TransitionCrossLineLeft));
                    OnPropertyChanged(nameof(TransitionContactLeftLineLeft));
                    OnPropertyChanged(nameof(TransitionContactRightLineLeft));
                    OnPropertyChanged(nameof(RightInteractionLayerWidth));
                    OnPropertyChanged(nameof(ParallelBranchWidth));
                    OnPropertyChanged(nameof(ParallelRightVerticalLeft));
                    OnPropertyChanged(nameof(ParallelRightConnectPointLeft));
                    OnPropertyChanged(nameof(ParallelInteractionLayerWidth));
                    OnPropertyChanged(nameof(Size)); // 通知Size属性变化
                }
            }
        }

        /// <summary>
        /// 动态高度 - 用于XAML绑定
        /// </summary>
        public double DynamicHeight
        {
            get => _dynamicHeight;
            set
            {
                if (SetProperty(ref _dynamicHeight, value))
                {
                    OnPropertyChanged(nameof(Size)); // 通知Size属性变化
                }
            }
        }

        /// <summary>
        /// 是否需要动态调整
        /// </summary>
        public bool RequiresDynamicAdjustment
        {
            get => _requiresDynamicAdjustment;
            set => SetProperty(ref _requiresDynamicAdjustment, value);
        }

        /// <summary>
        /// 是否为初始步骤（用于控制连接点可见性）
        /// </summary>
        public bool IsInitialStep
        {
            get => _isInitialStep;
            set => SetProperty(ref _isInitialStep, value);
        }

        // 动态计算的连接线坐标属性（根据统计表格精确调整）
        // 左侧竖直连接线 - 只有X方向需要动态绑定，Y方向在XAML中硬编码
        public double LeftLineX1 => 20;
        public double LeftLineX2 => 20;
        public double LeftLineLeft => 1;

        // 主连接横线 - 根据统计表格调整，只有X2需要动态调整
        public double MainHorizontalLineX1 => -103; // 起点坐标（保持原有坐标系统）
        /// <summary>
        /// 🔧 修复：主横线终点坐标，确保线条长度与DynamicWidth成正比
        /// 原公式：DynamicWidth - 141 导致线条长度不成比例
        /// 新公式：基于DynamicWidth按比例计算，确保147px对应44px，294px对应191px，线条长度成2倍关系
        /// </summary>
        public double MainHorizontalLineX2
        {
            get
            {
                // 🚀 超级简化：分支横线长度直接等于分支宽度
                // 
                // 理解：
                // - 分支横线的基础长度是147px
                // - 分支横线的起点坐标为-103px
                // - 分支横线的末端坐标为147-103=44px
                // - 2倍宽度时：横线长度294px，末端坐标294-103=191px
                // 
                // 计算：
                // - 横线长度 = 末端坐标 - 起点坐标 = X2 - (-103) = X2 + 103
                // - 所以：X2 = 横线长度 - 103 = DynamicWidth - 103
                // 
                // 结果：完美的1:1比例关系！
                double result = DynamicWidth - 103;
                
                System.Diagnostics.Debug.WriteLine($"[MainHorizontalLineX2] 分支宽度{DynamicWidth}px → 横线长度{DynamicWidth}px → 末端坐标{result:F1}px");
                return result;
            }
        }
        public double MainHorizontalLineLeft => 124; // 偏移坐标，硬编码（黑色字体）

        // 连接点动态位置 - 只有X方向动态，Y方向在XAML中硬编码
        public double LeftTopConnectPointLeft => 16;
        public double LeftBottomConnectPointLeft => 16;
        /// <summary>
        /// 🔧 修复：右侧上端连接点位置，基于备份文件中的正确静态偏移值
        /// 备份文件中：Canvas.Left="162"，主横线终点=168，相对偏移=-6px
        /// 新公式：基于主横线实际终点位置计算，确保与静态布局一致
        /// </summary>
        public double RightTopConnectPointLeft
        {
            get
            {
                // 🔧 修复：基于备份文件中的正确相对偏移值(-6px)
                double mainLineEndX = MainHorizontalLineLeft + MainHorizontalLineX2;
                double result = mainLineEndX - 6; // 备份文件中的正确相对偏移

                System.Diagnostics.Debug.WriteLine($"[RightTopConnectPointLeft] 主横线终点={mainLineEndX:F1}, 右上连接点位置={result:F1}");
                return result;
            }
        }

        /// <summary>
        /// 🔧 修复：右侧下端连接点位置，基于备份文件中的正确静态偏移值
        /// 备份文件中：Canvas.Left="162"，主横线终点=168，相对偏移=-6px
        /// 新公式：基于主横线实际终点位置计算，确保与静态布局一致
        /// </summary>
        public double RightBottomConnectPointLeft
        {
            get
            {
                // 🔧 修复：基于备份文件中的正确相对偏移值(-6px)
                double mainLineEndX = MainHorizontalLineLeft + MainHorizontalLineX2;
                double result = mainLineEndX - 6; // 备份文件中的正确相对偏移

                System.Diagnostics.Debug.WriteLine($"[RightBottomConnectPointLeft] 主横线终点={mainLineEndX:F1}, 右下连接点位置={result:F1}");
                return result;
            }
        }

        /// <summary>
        /// 🔧 修复：右侧连接线位置，基于备份文件中的正确静态偏移值
        /// 备份文件中：Canvas.Left="165.5"，主横线终点=168，相对偏移=-2.5px
        /// 新公式：基于主横线实际终点位置计算，确保与静态布局一致
        /// </summary>
        public double RightConnectionLineLeft
        {
            get
            {
                // 🔧 修复：基于备份文件中的正确相对偏移值(-2.5px)
                double mainLineEndX = MainHorizontalLineLeft + MainHorizontalLineX2;
                double result = mainLineEndX - 2.5; // 备份文件中的正确相对偏移

                System.Diagnostics.Debug.WriteLine($"[RightConnectionLineLeft] 主横线终点={mainLineEndX:F1}, 右侧竖线位置={result:F1}");
                return result;
            }
        }

        // 转换条件相关动态位置 - 根据统计表格动态调整
        public double TransitionLineLeft => DynamicWidth - 78; // 动态调整（原值107）
        /// <summary>
        /// 🔧 修复：转换条件标识符位置，确保与主横线终点对齐
        /// 原公式：DynamicWidth - 9 导致T1标识符悬空
        /// 新公式：基于主横线实际终点位置计算，确保T1紧贴主横线末端
        /// </summary>
        public double TransitionIdentifierLeft
        {
            get
            {
                // 🔧 修复：基于备份文件中的正确相对偏移值(+8px)
                // 备份文件中：Canvas.Left="176"，主横线终点=168，相对偏移=+8px
                // 新公式：基于主横线实际终点位置计算，确保与静态布局一致
                double mainLineEndX = MainHorizontalLineLeft + MainHorizontalLineX2;
                double result = mainLineEndX + 8; // 备份文件中的正确相对偏移
                
                System.Diagnostics.Debug.WriteLine($"[TransitionIdentifierLeft] 主横线终点={mainLineEndX:F1}, T1位置={result:F1}");
                return result;
            }
        }

        // 转换条件的连接虚线 - 根据统计表格
        public double TransitionConnectionLineX1 => 29; // 起点坐标，硬编码
        public double TransitionConnectionLineX2 => 60; // 终点坐标，硬编码
        /// <summary>
        /// 🔧 修复：转换条件连接虚线位置，基于备份文件中的正确静态偏移值
        /// 备份文件中：Canvas.Left="107"，主横线终点=168，相对偏移=-61px
        /// 新公式：基于主横线实际终点位置计算，确保与静态布局一致
        /// </summary>
        public double TransitionConnectionLineLeft
        {
            get
            {
                // 🔧 修复：基于备份文件中的正确相对偏移值(-61px)
                double mainLineEndX = MainHorizontalLineLeft + MainHorizontalLineX2;
                double result = mainLineEndX - 61; // 备份文件中的正确相对偏移

                System.Diagnostics.Debug.WriteLine($"[TransitionConnectionLineLeft] 主横线终点={mainLineEndX:F1}, 连接虚线位置={result:F1}");
                return result;
            }
        }

        // 转换条件的十字横线 - 根据统计表格
        public double TransitionCrossLineX1 => 55; // 起点坐标，硬编码
        public double TransitionCrossLineX2 => 65; // 终点坐标，硬编码
        /// <summary>
        /// 🔧 修复：转换条件十字横线位置，基于备份文件中的正确静态偏移值
        /// 备份文件中：Canvas.Left="107"，主横线终点=168，相对偏移=-61px
        /// 新公式：基于主横线实际终点位置计算，确保与静态布局一致
        /// </summary>
        public double TransitionCrossLineLeft
        {
            get
            {
                // 🔧 修复：基于备份文件中的正确相对偏移值(-61px)
                double mainLineEndX = MainHorizontalLineLeft + MainHorizontalLineX2;
                double result = mainLineEndX - 61; // 备份文件中的正确相对偏移，与连接虚线保持一致

                System.Diagnostics.Debug.WriteLine($"[TransitionCrossLineLeft] 主横线终点={mainLineEndX:F1}, 十字横线位置={result:F1}");
                return result;
            }
        }

        /// <summary>
        /// 🔧 修复：转换条件触点左侧竖线位置，基于备份文件中的正确静态偏移值
        /// 备份文件中：Canvas.Left="128"，主横线终点=168，相对偏移=-40px
        /// 新公式：基于主横线实际终点位置计算，确保与静态布局一致
        /// </summary>
        public double TransitionContactLeftLineLeft
        {
            get
            {
                // 🔧 修复：基于备份文件中的正确相对偏移值(-40px)
                double mainLineEndX = MainHorizontalLineLeft + MainHorizontalLineX2;
                double result = mainLineEndX - 40; // 备份文件中的正确相对偏移

                System.Diagnostics.Debug.WriteLine($"[TransitionContactLeftLineLeft] 主横线终点={mainLineEndX:F1}, 触点左竖线位置={result:F1}");
                return result;
            }
        }

        /// <summary>
        /// 🔧 修复：转换条件触点右侧竖线位置，基于备份文件中的正确静态偏移值
        /// 备份文件中：Canvas.Left="135"，主横线终点=168，相对偏移=-33px
        /// 新公式：基于主横线实际终点位置计算，确保与静态布局一致
        /// </summary>
        public double TransitionContactRightLineLeft
        {
            get
            {
                // 🔧 修复：基于备份文件中的正确相对偏移值(-33px)
                double mainLineEndX = MainHorizontalLineLeft + MainHorizontalLineX2;
                double result = mainLineEndX - 33; // 备份文件中的正确相对偏移

                System.Diagnostics.Debug.WriteLine($"[TransitionContactRightLineLeft] 主横线终点={mainLineEndX:F1}, 触点右竖线位置={result:F1}");
                return result;
            }
        }

        // 转换条件的触点左侧竖线
        public double TransitionLeftVerticalLineLeft => 128; // 可能需要动态调整

        // 转换条件的触点右侧竖线
        public double TransitionRightVerticalLineLeft => 135; // 可能需要动态调整

        // 右侧交互层 - 根据统计表格，只有Width需要动态调整
        public double RightInteractionLayerWidth => DynamicWidth - 50; // 改变整体宽度（原135）
        // Canvas.Left=49 保持硬编码，不需要动态调整

        // 并行分支特有的动态属性 - 按照统计表格思路调整
        // 只有真正需要随宽度变化的参数才动态调整
        public double ParallelBranchWidth => DynamicWidth - 54; // 双线宽度需要动态调整

        // 右侧元素需要动态调整位置（类似选择分支的右侧元素）
        public double ParallelRightVerticalLeft => DynamicWidth - 14; // 右侧垂直线位置
        public double ParallelRightConnectPointLeft => DynamicWidth - 17; // 右侧连接点位置

        // 并行分支交互层宽度 - 需要随分支宽度动态调整
        public double ParallelInteractionLayerWidth => DynamicWidth - 24; // 交互层宽度（原176 = 200 - 24）

        // 左侧元素和固定位置保持硬编码（在XAML中直接写死）
        // ParallelTopLineLeft, ParallelBottomLineLeft, ParallelLeftVerticalLeft 等
        // ParallelLeftTopConnectPointLeft, ParallelLeftConnectPointLeft 等
        // 所有Y方向坐标都在XAML中硬编码

        #endregion

        // 分支信息
        public List<string> InputStepIds { get; set; } = new();
        public List<string> OutputStepIds { get; set; } = new();

        // 命令
        public ICommand? SelectCommand { get; set; }
        public ICommand? DeleteCommand { get; set; }
        public ICommand? StartConnectionCommand { get; set; }
        public ICommand? EditPropertiesCommand { get; set; }
        public ICommand ToggleBranchTypeCommand { get; }
        public ICommand ToggleConvergenceCommand { get; }

        /// <summary>
        /// 连接点适配器集合
        /// </summary>
        public System.Collections.ObjectModel.ObservableCollection<SFCConnectPointAdapter> ConnectPointAdapters { get; } = new System.Collections.ObjectModel.ObservableCollection<SFCConnectPointAdapter>();

        /// <summary>
        /// 获取对应的EnhancedSFCViewModel实例的委托
        /// </summary>
        public Func<ViewModels.EnhancedSFCViewModel?>? GetEnhancedSFCViewModel { get; set; }

        public SFCBranchViewModel()
        {
            ToggleBranchTypeCommand = new RelayCommand(ToggleBranchType);
            ToggleConvergenceCommand = new RelayCommand(ToggleConvergence);

            // 初始化连接点适配器
            InitializeConnectPointAdapters();

            // 初始化动态布局属性
            InitializeDynamicLayoutProperties();
        }

        /// <summary>
        /// 初始化动态布局属性
        /// </summary>
        /// <summary>
        /// 初始化动态布局属性
        /// </summary>
        /// <summary>
        /// 初始化动态布局属性
        /// </summary>
        private void InitializeDynamicLayoutProperties()
        {
            // 根据分支类型设置默认动态尺寸 - 使用属性setter触发PropertyChanged通知
            if (BranchType == SFCBranchType.Selection)
            {
                DynamicWidth = 147.0; // 选择分支默认宽度（超级简化基准） - 使用属性setter
                DynamicHeight = 80.0; // 选择分支默认高度 - 使用属性setter
                
                // 🔧 修复：确保选择分支有正确的转换条件编号，但不强制设置为1
                // 只有当编号确实为0或无效时才需要获取正确的编号
                if (InternalTransitionNumber <= 0)
                {
                    // 尝试从EnhancedSFCViewModel获取正确的编号
                    var enhancedViewModel = GetEnhancedSFCViewModel?.Invoke();
                    if (enhancedViewModel != null)
                    {
                        // 使用GetNextTransitionNumber获取正确的编号，避免冲突
                        var nextNumber = GetNextTransitionNumberFromEnhanced(enhancedViewModel);
                        InternalTransitionNumber = nextNumber;
                        System.Diagnostics.Debug.WriteLine($"[SFCBranchViewModel] 🔧 修复：为选择分支分配正确的转换条件编号 T{InternalTransitionNumber}");
                    }
                    else
                    {
                        // 如果无法获取EnhancedSFCViewModel，使用默认值1
                        InternalTransitionNumber = 1;
                        System.Diagnostics.Debug.WriteLine($"[SFCBranchViewModel] 🔧 临时修复：为选择分支设置默认转换条件编号 T{InternalTransitionNumber}");
                    }
                }
            }
            else if (BranchType == SFCBranchType.Parallel)
            {
                DynamicWidth = 200.0; // 并行分支默认宽度 - 使用属性setter
                DynamicHeight = 50.0; // 并行分支默认高度 - 使用属性setter
            }

            System.Diagnostics.Debug.WriteLine($"[SFCBranchViewModel] 动态布局属性初始化: {Id} -> {DynamicWidth}x{DynamicHeight}");

            // 调试输出关键属性值
            System.Diagnostics.Debug.WriteLine($"[SFCBranchViewModel] 🔍 关键属性值调试:");
            System.Diagnostics.Debug.WriteLine($"  - LeftLineX1: {LeftLineX1}, LeftLineX2: {LeftLineX2}");
            System.Diagnostics.Debug.WriteLine($"  - LeftLineLeft: {LeftLineLeft}");
            System.Diagnostics.Debug.WriteLine($"  - MainHorizontalLineX1: {MainHorizontalLineX1}, MainHorizontalLineX2: {MainHorizontalLineX2}");
            System.Diagnostics.Debug.WriteLine($"  - MainHorizontalLineLeft: {MainHorizontalLineLeft}");
            System.Diagnostics.Debug.WriteLine($"  - ViewType: {ViewType}, IsSelected: {IsSelected}");
            System.Diagnostics.Debug.WriteLine($"  - InternalTransitionNumber: {InternalTransitionNumber}, InternalTransitionHasLabel: {InternalTransitionHasLabel}");
        }
        
        /// <summary>
        /// 初始化连接点适配器
        /// </summary>
        private void InitializeConnectPointAdapters()
        {
            // 🔧 关键修复：清空现有适配器，避免重复添加
            ConnectPointAdapters.Clear();

            // 🔧 关键修复：根据分支类型确定正确的ElementType
            var elementType = BranchType == SFCBranchType.Parallel ? SFCElementType.ParallelBranch : SFCElementType.SelectionBranch;

            System.Diagnostics.Debug.WriteLine($"[SFCBranchViewModel] 初始化适配器: BranchType={BranchType}, ElementType={elementType}");

            // 🔧 选择分支适配器配置
            // UI连接点索引：LeftTop=0, LeftBottom=1, RightTop=2, RightBottom=3

            // 创建左侧输入连接点适配器 (UI Index=0)
            var leftInputAdapter = new SFCConnectPointAdapter(
                Id,
                elementType,
                ConnectPointDirection.Input,
                0,
                SFCDataFlowType.ControlFlow);

            // 🔧 关键修复：创建左侧连接点适配器 (UI Index=1)
            // 根据CreateBranchChainConnection逻辑，扩展选择分支的索引1用作输入
            var leftOutputAdapter = new SFCConnectPointAdapter(
                Id,
                elementType,
                ConnectPointDirection.Input,   // 🔧 关键修复：索引1用作输入（扩展分支连接的目标）
                1,
                SFCDataFlowType.ControlFlow);

            // 🔧 关键修复：创建右侧输出连接点适配器 (UI Index=2)
            // 索引2用于分支链连接，应该是Output类型
            var rightOutputAdapter2 = new SFCConnectPointAdapter(
                Id,
                elementType,
                ConnectPointDirection.Output,  // 🔧 关键修复：从Input改为Output
                2,
                SFCDataFlowType.ControlFlow);

            // 创建右侧输出连接点适配器 (UI Index=3)
            var rightOutputAdapter = new SFCConnectPointAdapter(
                Id,
                elementType,
                ConnectPointDirection.Output,
                3,
                SFCDataFlowType.ControlFlow);

            // 🔧 并行分支特殊处理：并行分支只有3个连接点，需要特殊的适配器配置
            if (BranchType == SFCBranchType.Parallel)
            {
                // 🔧 关键修复：根据CreateBranchChainConnection中的连接逻辑调整适配器配置
                // 连接逻辑：源分支索引0（输出）-> 目标分支索引1（输入）
                // 因此：索引0应该是输出，索引1应该是输入

                var parallelLeftTopAdapter = new SFCConnectPointAdapter(
                    Id,
                    SFCElementType.ParallelBranch,
                    ConnectPointDirection.Output,  // 🔧 关键修复：索引0用作输出（分支链连接的源）
                    0,
                    SFCDataFlowType.ControlFlow);

                var parallelLeftParallelAdapter = new SFCConnectPointAdapter(
                    Id,
                    SFCElementType.ParallelBranch,
                    ConnectPointDirection.Input,   // 🔧 关键修复：索引1用作输入（分支链连接的目标）
                    1,
                    SFCDataFlowType.ControlFlow);

                var parallelRightParallelAdapter = new SFCConnectPointAdapter(
                    Id,
                    SFCElementType.ParallelBranch,
                    ConnectPointDirection.Output,
                    2,
                    SFCDataFlowType.ControlFlow);

                ConnectPointAdapters.Add(parallelLeftTopAdapter);      // 索引0
                ConnectPointAdapters.Add(parallelLeftParallelAdapter); // 索引1
                ConnectPointAdapters.Add(parallelRightParallelAdapter);// 索引2

                System.Diagnostics.Debug.WriteLine($"[SFCBranchViewModel] ✅ 并行分支适配器配置完成，数量: {ConnectPointAdapters.Count}");
            }
            else
            {
                // 🔧 关键修复：选择分支适配器配置 - 根据CreateBranchChainConnection连接逻辑配置
                // 连接逻辑：源分支索引2（输出）-> 目标分支索引1（输入）或索引0（输入）
                ConnectPointAdapters.Add(leftInputAdapter);     // 索引0: LeftTop (Input) - 初始分支连接目标
                ConnectPointAdapters.Add(leftOutputAdapter);    // 索引1: LeftBottom (Input) - 扩展分支连接目标
                ConnectPointAdapters.Add(rightOutputAdapter2);  // 索引2: RightTop (Output) - 分支链连接源
                ConnectPointAdapters.Add(rightOutputAdapter);   // 索引3: RightBottom (Output) - 普通输出

                System.Diagnostics.Debug.WriteLine($"[SFCBranchViewModel] ✅ 选择分支适配器配置完成，数量: {ConnectPointAdapters.Count}");
                System.Diagnostics.Debug.WriteLine($"[SFCBranchViewModel] 🔧 索引1适配器类型: {ConnectPointAdapters[1].Direction} (修复为Input)");
                System.Diagnostics.Debug.WriteLine($"[SFCBranchViewModel] 🔧 索引2适配器类型: {ConnectPointAdapters[2].Direction} (修复为Output)");
            }
        }

        /// <summary>
        /// 切换分支类型（选择/并行）
        /// </summary>
        private void ToggleBranchType()
        {
            BranchType = BranchType == SFCBranchType.Selection
                ? SFCBranchType.Parallel
                : SFCBranchType.Selection;
        }

        /// <summary>
        /// 切换汇聚状态
        /// </summary>
        private void ToggleConvergence()
        {
            IsConvergence = !IsConvergence;
        }

        /// <summary>
        /// 更新动态布局尺寸并触发相关属性通知
        /// </summary>
        /// <param name="newWidth">新宽度</param>
        /// <param name="newHeight">新高度</param>
        public void UpdateDynamicSize(double newWidth, double newHeight)
        {
            var oldWidth = DynamicWidth;
            var oldHeight = DynamicHeight;

            DynamicWidth = newWidth;
            DynamicHeight = newHeight;

            // 如果尺寸发生变化，触发所有依赖动态尺寸的属性通知
            if (Math.Abs(oldWidth - newWidth) > 0.1 || Math.Abs(oldHeight - newHeight) > 0.1)
            {
                // 选择分支相关属性 - 根据统计表格精确更新
                OnPropertyChanged(nameof(MainHorizontalLineLeft)); // 主横线偏移
                OnPropertyChanged(nameof(RightTopConnectPointLeft)); // 右侧连接点
                OnPropertyChanged(nameof(RightBottomConnectPointLeft)); // 右侧连接点
                OnPropertyChanged(nameof(RightConnectionLineLeft)); // 右侧连接线
                OnPropertyChanged(nameof(TransitionIdentifierLeft)); // 转换标识符
                OnPropertyChanged(nameof(RightInteractionLayerWidth)); // 右侧交互层宽度

                // 转换条件相关属性（如果需要动态调整）
                // 注意：根据统计表格，这些可能不需要动态调整，待进一步确认
                // OnPropertyChanged(nameof(TransitionConnectionLineLeft));
                // OnPropertyChanged(nameof(TransitionCrossLineLeft));
                // OnPropertyChanged(nameof(TransitionLeftVerticalLineLeft));
                // OnPropertyChanged(nameof(TransitionRightVerticalLineLeft));

                // 并行分支相关属性 - 只通知真正需要动态调整的属性
                OnPropertyChanged(nameof(ParallelBranchWidth));
                OnPropertyChanged(nameof(ParallelRightVerticalLeft));
                OnPropertyChanged(nameof(ParallelRightConnectPointLeft));
                OnPropertyChanged(nameof(ParallelInteractionLayerWidth));

                System.Diagnostics.Debug.WriteLine($"[SFCBranchViewModel] 动态尺寸更新: {Id} -> {newWidth}x{newHeight}");
            }
        }

        /// <summary>
        /// 🔧 关键修复：同步位置到对应的Model
        /// </summary>
        /// <param name="newPosition">新位置</param>
        private void SyncPositionToModel(Point newPosition)
        {
            try
            {
                var enhancedViewModel = GetEnhancedSFCViewModel?.Invoke();
                if (enhancedViewModel != null)
                {
                    // 通过ID查找对应的Model
                    var correspondingModel = enhancedViewModel.Branches.FirstOrDefault(b => b.Id == Id);
                    if (correspondingModel != null)
                    {
                        correspondingModel.Position = newPosition;
                        System.Diagnostics.Debug.WriteLine($"[SFCBranchViewModel] 🔧 位置同步成功: ViewModel({Id}) -> Model, 新位置={newPosition}");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"[SFCBranchViewModel] ⚠️ 未找到对应的Model: {Id}");
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"[SFCBranchViewModel] ⚠️ 无法获取EnhancedSFCViewModel实例");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[SFCBranchViewModel] ❌ 位置同步失败: {ex.Message}");
            }
        }
    }
} 